# Details

Date : 2025-05-28 13:45:17

Directory c:\\PFE_PROJECT\\ReactJs\\omar_pfe\\omar_cnss

Total : 62 files,  24530 codes, 847 comments, 1001 blanks, all 26378 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [Dockerfile](/Dockerfile) | Docker | 13 | 12 | 12 | 37 |
| [README.md](/README.md) | Markdown | 393 | 0 | 128 | 521 |
| [backend/Dockerfile](/backend/Dockerfile) | Docker | 35 | 12 | 12 | 59 |
| [backend/app/\_\_init\_\_.py](/backend/app/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [backend/app/api/deps.py](/backend/app/api/deps.py) | Python | 62 | 54 | 15 | 131 |
| [backend/app/api/v1/\_\_init\_\_.py](/backend/app/api/v1/__init__.py) | Python | 8 | 6 | 5 | 19 |
| [backend/app/api/v1/auth.py](/backend/app/api/v1/auth.py) | Python | 81 | 59 | 20 | 160 |
| [backend/app/api/v1/cnss.py](/backend/app/api/v1/cnss.py) | Python | 404 | 49 | 48 | 501 |
| [backend/app/core/config.py](/backend/app/core/config.py) | Python | 40 | 17 | 16 | 73 |
| [backend/app/core/database.py](/backend/app/core/database.py) | Python | 23 | 15 | 11 | 49 |
| [backend/app/core/security.py](/backend/app/core/security.py) | Python | 68 | 75 | 21 | 164 |
| [backend/app/main.py](/backend/app/main.py) | Python | 60 | 12 | 15 | 87 |
| [backend/app/models/\_\_init\_\_.py](/backend/app/models/__init__.py) | Python | 11 | 3 | 2 | 16 |
| [backend/app/models/cnss.py](/backend/app/models/cnss.py) | Python | 125 | 18 | 38 | 181 |
| [backend/app/models/employer.py](/backend/app/models/employer.py) | Python | 42 | 10 | 12 | 64 |
| [backend/app/models/service.py](/backend/app/models/service.py) | Python | 45 | 14 | 18 | 77 |
| [backend/app/models/user.py](/backend/app/models/user.py) | Python | 29 | 8 | 11 | 48 |
| [backend/app/schemas/cnss.py](/backend/app/schemas/cnss.py) | Python | 179 | 13 | 36 | 228 |
| [backend/app/schemas/user.py](/backend/app/schemas/user.py) | Python | 51 | 12 | 22 | 85 |
| [backend/production\_main.py](/backend/production_main.py) | Python | 455 | 38 | 55 | 548 |
| [backend/requirements.txt](/backend/requirements.txt) | pip requirements | 29 | 14 | 14 | 57 |
| [backend/simple\_main.py](/backend/simple_main.py) | Python | 140 | 21 | 25 | 186 |
| [backend/test\_server.py](/backend/test_server.py) | Python | 99 | 13 | 19 | 131 |
| [database/init/01\_create\_user.sql](/database/init/01_create_user.sql) | MS SQL | 23 | 8 | 8 | 39 |
| [database/init/02\_seed\_data.sql](/database/init/02_seed_data.sql) | MS SQL | 126 | 10 | 11 | 147 |
| [docker-compose.yml](/docker-compose.yml) | YAML | 113 | 5 | 8 | 126 |
| [nginx.conf](/nginx.conf) | Properties | 35 | 7 | 8 | 50 |
| [package-lock.json](/package-lock.json) | JSON | 17,963 | 0 | 1 | 17,964 |
| [package.json](/package.json) | JSON | 49 | 0 | 1 | 50 |
| [postcss.config.js](/postcss.config.js) | JavaScript | 6 | 0 | 1 | 7 |
| [public/index.html](/public/index.html) | HTML | 20 | 23 | 1 | 44 |
| [public/manifest.json](/public/manifest.json) | JSON | 25 | 0 | 1 | 26 |
| [scripts/dev-setup.bat](/scripts/dev-setup.bat) | Batch | 103 | 10 | 21 | 134 |
| [scripts/dev-setup.sh](/scripts/dev-setup.sh) | Shell Script | 130 | 21 | 32 | 183 |
| [src/App.css](/src/App.css) | PostCSS | 33 | 0 | 6 | 39 |
| [src/App.js](/src/App.js) | JavaScript | 171 | 8 | 13 | 192 |
| [src/App.test.js](/src/App.test.js) | JavaScript | 7 | 0 | 2 | 9 |
| [src/components/auth/LoginForm.jsx](/src/components/auth/LoginForm.jsx) | JavaScript JSX | 171 | 16 | 20 | 207 |
| [src/components/auth/LoginForm.test.js](/src/components/auth/LoginForm.test.js) | JavaScript | 118 | 3 | 28 | 149 |
| [src/components/auth/ProtectedRoute.jsx](/src/components/auth/ProtectedRoute.jsx) | JavaScript JSX | 41 | 7 | 7 | 55 |
| [src/components/layout/Layout.jsx](/src/components/layout/Layout.jsx) | JavaScript JSX | 31 | 8 | 8 | 47 |
| [src/components/layout/Navbar.jsx](/src/components/layout/Navbar.jsx) | JavaScript JSX | 199 | 11 | 14 | 224 |
| [src/components/layout/Sidebar.jsx](/src/components/layout/Sidebar.jsx) | JavaScript JSX | 178 | 12 | 14 | 204 |
| [src/components/modals/EmployerModal.jsx](/src/components/modals/EmployerModal.jsx) | JavaScript JSX | 296 | 11 | 19 | 326 |
| [src/components/ui/Button.jsx](/src/components/ui/Button.jsx) | JavaScript JSX | 64 | 11 | 7 | 82 |
| [src/components/ui/Card.jsx](/src/components/ui/Card.jsx) | JavaScript JSX | 54 | 20 | 10 | 84 |
| [src/components/ui/Input.jsx](/src/components/ui/Input.jsx) | JavaScript JSX | 53 | 10 | 5 | 68 |
| [src/components/ui/LoadingSpinner.jsx](/src/components/ui/LoadingSpinner.jsx) | JavaScript JSX | 61 | 13 | 7 | 81 |
| [src/components/ui/Modal.jsx](/src/components/ui/Modal.jsx) | JavaScript JSX | 131 | 25 | 15 | 171 |
| [src/contexts/AuthContext.jsx](/src/contexts/AuthContext.jsx) | JavaScript JSX | 144 | 33 | 22 | 199 |
| [src/contexts/ThemeContext.jsx](/src/contexts/ThemeContext.jsx) | JavaScript JSX | 125 | 30 | 25 | 180 |
| [src/index.css](/src/index.css) | PostCSS | 32 | 0 | 9 | 41 |
| [src/index.js](/src/index.js) | JavaScript | 12 | 3 | 3 | 18 |
| [src/logo.svg](/src/logo.svg) | XML | 1 | 0 | 0 | 1 |
| [src/pages/Dashboard.jsx](/src/pages/Dashboard.jsx) | JavaScript JSX | 249 | 13 | 20 | 282 |
| [src/pages/Employers.jsx](/src/pages/Employers.jsx) | JavaScript JSX | 307 | 11 | 22 | 340 |
| [src/pages/Services.jsx](/src/pages/Services.jsx) | JavaScript JSX | 329 | 10 | 22 | 361 |
| [src/pages/Settings.jsx](/src/pages/Settings.jsx) | JavaScript JSX | 515 | 14 | 35 | 564 |
| [src/reportWebVitals.js](/src/reportWebVitals.js) | JavaScript | 12 | 0 | 2 | 14 |
| [src/services/api.js](/src/services/api.js) | JavaScript | 98 | 12 | 15 | 125 |
| [src/setupTests.js](/src/setupTests.js) | JavaScript | 1 | 4 | 1 | 6 |
| [tailwind.config.js](/tailwind.config.js) | JavaScript | 112 | 1 | 1 | 114 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)