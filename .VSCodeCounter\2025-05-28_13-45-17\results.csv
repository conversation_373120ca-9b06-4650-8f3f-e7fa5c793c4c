"filename", "language", "Markdown", "<PERSON><PERSON><PERSON>", "Docker", "JavaScript", "JSON", "MS SQL", "Properties", "pip requirements", "Python", "HTML", "JavaScript JSX", "PostCSS", "Shell Script", "Batch", "XML", "comment", "blank", "total"
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\Dockerfile", "Docker", 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 12, 37
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\README.md", "Markdown", 393, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128, 521
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\Dockerfile", "Docker", 0, 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 12, 59
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\app\__init__.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 1, 3
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\app\api\deps.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 54, 15, 131
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\app\api\v1\__init__.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 6, 5, 19
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\app\api\v1\auth.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 81, 0, 0, 0, 0, 0, 0, 59, 20, 160
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\app\api\v1\cnss.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 404, 0, 0, 0, 0, 0, 0, 49, 48, 501
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\app\core\config.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 17, 16, 73
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\app\core\database.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 23, 0, 0, 0, 0, 0, 0, 15, 11, 49
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\app\core\security.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 0, 75, 21, 164
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\app\main.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 12, 15, 87
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\app\models\__init__.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 3, 2, 16
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\app\models\cnss.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 125, 0, 0, 0, 0, 0, 0, 18, 38, 181
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\app\models\employer.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 42, 0, 0, 0, 0, 0, 0, 10, 12, 64
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\app\models\service.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 45, 0, 0, 0, 0, 0, 0, 14, 18, 77
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\app\models\user.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 8, 11, 48
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\app\schemas\cnss.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 179, 0, 0, 0, 0, 0, 0, 13, 36, 228
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\app\schemas\user.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 51, 0, 0, 0, 0, 0, 0, 12, 22, 85
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\production_main.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 455, 0, 0, 0, 0, 0, 0, 38, 55, 548
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\requirements.txt", "pip requirements", 0, 0, 0, 0, 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 14, 14, 57
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\simple_main.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 140, 0, 0, 0, 0, 0, 0, 21, 25, 186
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\backend\test_server.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 99, 0, 0, 0, 0, 0, 0, 13, 19, 131
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\database\init\01_create_user.sql", "MS SQL", 0, 0, 0, 0, 0, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 39
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\database\init\02_seed_data.sql", "MS SQL", 0, 0, 0, 0, 0, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 11, 147
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\docker-compose.yml", "YAML", 0, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 8, 126
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\nginx.conf", "Properties", 0, 0, 0, 0, 0, 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 7, 8, 50
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\package-lock.json", "JSON", 0, 0, 0, 0, 17963, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 17964
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\package.json", "JSON", 0, 0, 0, 0, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 50
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\postcss.config.js", "JavaScript", 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 7
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\public\index.html", "HTML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 23, 1, 44
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\public\manifest.json", "JSON", 0, 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 26
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\scripts\dev-setup.bat", "Batch", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 103, 0, 10, 21, 134
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\scripts\dev-setup.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 130, 0, 0, 21, 32, 183
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\App.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 0, 0, 0, 0, 6, 39
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\App.js", "JavaScript", 0, 0, 0, 171, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 13, 192
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\App.test.js", "JavaScript", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 9
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\components\auth\LoginForm.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 171, 0, 0, 0, 0, 16, 20, 207
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\components\auth\LoginForm.test.js", "JavaScript", 0, 0, 0, 118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 28, 149
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\components\auth\ProtectedRoute.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 0, 0, 0, 0, 7, 7, 55
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\components\layout\Layout.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 0, 0, 0, 0, 8, 8, 47
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\components\layout\Navbar.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 199, 0, 0, 0, 0, 11, 14, 224
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\components\layout\Sidebar.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 178, 0, 0, 0, 0, 12, 14, 204
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\components\modals\EmployerModal.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 0, 0, 0, 0, 11, 19, 326
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\components\ui\Button.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 0, 0, 0, 0, 11, 7, 82
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\components\ui\Card.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 54, 0, 0, 0, 0, 20, 10, 84
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\components\ui\Input.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 10, 5, 68
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\components\ui\LoadingSpinner.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 0, 0, 0, 0, 13, 7, 81
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\components\ui\Modal.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 131, 0, 0, 0, 0, 25, 15, 171
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\contexts\AuthContext.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 144, 0, 0, 0, 0, 33, 22, 199
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\contexts\ThemeContext.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 125, 0, 0, 0, 0, 30, 25, 180
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\index.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 0, 0, 0, 0, 9, 41
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\index.js", "JavaScript", 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 18
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\logo.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\pages\Dashboard.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 249, 0, 0, 0, 0, 13, 20, 282
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\pages\Employers.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 307, 0, 0, 0, 0, 11, 22, 340
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\pages\Services.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 329, 0, 0, 0, 0, 10, 22, 361
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\pages\Settings.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 515, 0, 0, 0, 0, 14, 35, 564
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\reportWebVitals.js", "JavaScript", 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 14
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\services\api.js", "JavaScript", 0, 0, 0, 98, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 15, 125
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\src\setupTests.js", "JavaScript", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 1, 6
"c:\PFE_PROJECT\ReactJs\omar_pfe\omar_cnss\tailwind.config.js", "JavaScript", 0, 0, 0, 112, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 114
"Total", "-", 393, 113, 48, 537, 18037, 149, 35, 29, 1922, 20, 2948, 65, 130, 103, 1, 847, 1001, 26378