{"file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/README.md": {"language": "<PERSON><PERSON>", "code": 393, "comment": 0, "blank": 128}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/docker-compose.yml": {"language": "YAML", "code": 113, "comment": 5, "blank": 8}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/Dockerfile": {"language": "<PERSON>er", "code": 13, "comment": 12, "blank": 12}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/tailwind.config.js": {"language": "JavaScript", "code": 112, "comment": 1, "blank": 1}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/package.json": {"language": "JSON", "code": 49, "comment": 0, "blank": 1}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/database/init/02_seed_data.sql": {"language": "MS SQL", "code": 126, "comment": 10, "blank": 11}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/postcss.config.js": {"language": "JavaScript", "code": 6, "comment": 0, "blank": 1}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/nginx.conf": {"language": "Properties", "code": 35, "comment": 7, "blank": 8}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/database/init/01_create_user.sql": {"language": "MS SQL", "code": 23, "comment": 8, "blank": 8}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/setupTests.js": {"language": "JavaScript", "code": 1, "comment": 4, "blank": 1}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/public/manifest.json": {"language": "JSON", "code": 25, "comment": 0, "blank": 1}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/requirements.txt": {"language": "pip requirements", "code": 29, "comment": 14, "blank": 14}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/simple_main.py": {"language": "Python", "code": 140, "comment": 21, "blank": 25}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/app/schemas/user.py": {"language": "Python", "code": 51, "comment": 12, "blank": 22}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/production_main.py": {"language": "Python", "code": 455, "comment": 38, "blank": 55}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/app/schemas/cnss.py": {"language": "Python", "code": 179, "comment": 13, "blank": 36}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/app/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/app/main.py": {"language": "Python", "code": 60, "comment": 12, "blank": 15}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/test_server.py": {"language": "Python", "code": 99, "comment": 13, "blank": 19}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/services/api.js": {"language": "JavaScript", "code": 98, "comment": 12, "blank": 15}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/Dockerfile": {"language": "<PERSON>er", "code": 35, "comment": 12, "blank": 12}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/app/api/deps.py": {"language": "Python", "code": 62, "comment": 54, "blank": 15}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/app/core/security.py": {"language": "Python", "code": 68, "comment": 75, "blank": 21}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/app/core/database.py": {"language": "Python", "code": 23, "comment": 15, "blank": 11}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/app/models/__init__.py": {"language": "Python", "code": 11, "comment": 3, "blank": 2}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/app/core/config.py": {"language": "Python", "code": 40, "comment": 17, "blank": 16}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/app/models/service.py": {"language": "Python", "code": 45, "comment": 14, "blank": 18}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/app/models/user.py": {"language": "Python", "code": 29, "comment": 8, "blank": 11}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/app/models/cnss.py": {"language": "Python", "code": 125, "comment": 18, "blank": 38}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/app/models/employer.py": {"language": "Python", "code": 42, "comment": 10, "blank": 12}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/reportWebVitals.js": {"language": "JavaScript", "code": 12, "comment": 0, "blank": 2}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/public/index.html": {"language": "HTML", "code": 20, "comment": 23, "blank": 1}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/pages/Settings.jsx": {"language": "JavaScript JSX", "code": 515, "comment": 14, "blank": 35}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/pages/Services.jsx": {"language": "JavaScript JSX", "code": 329, "comment": 10, "blank": 22}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/pages/Employers.jsx": {"language": "JavaScript JSX", "code": 307, "comment": 11, "blank": 22}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/app/api/v1/__init__.py": {"language": "Python", "code": 8, "comment": 6, "blank": 5}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/pages/Dashboard.jsx": {"language": "JavaScript JSX", "code": 249, "comment": 13, "blank": 20}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/app/api/v1/cnss.py": {"language": "Python", "code": 404, "comment": 49, "blank": 48}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/backend/app/api/v1/auth.py": {"language": "Python", "code": 81, "comment": 59, "blank": 20}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/index.js": {"language": "JavaScript", "code": 12, "comment": 3, "blank": 3}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/contexts/ThemeContext.jsx": {"language": "JavaScript JSX", "code": 125, "comment": 30, "blank": 25}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/index.css": {"language": "PostCSS", "code": 32, "comment": 0, "blank": 9}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/scripts/dev-setup.sh": {"language": "<PERSON> Script", "code": 130, "comment": 21, "blank": 32}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/contexts/AuthContext.jsx": {"language": "JavaScript JSX", "code": 144, "comment": 33, "blank": 22}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/App.js": {"language": "JavaScript", "code": 171, "comment": 8, "blank": 13}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/scripts/dev-setup.bat": {"language": "<PERSON><PERSON>", "code": 103, "comment": 10, "blank": 21}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/App.css": {"language": "PostCSS", "code": 33, "comment": 0, "blank": 6}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/App.test.js": {"language": "JavaScript", "code": 7, "comment": 0, "blank": 2}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/components/ui/LoadingSpinner.jsx": {"language": "JavaScript JSX", "code": 61, "comment": 13, "blank": 7}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/components/ui/Card.jsx": {"language": "JavaScript JSX", "code": 54, "comment": 20, "blank": 10}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/components/ui/Modal.jsx": {"language": "JavaScript JSX", "code": 131, "comment": 25, "blank": 15}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/components/ui/Input.jsx": {"language": "JavaScript JSX", "code": 53, "comment": 10, "blank": 5}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/components/ui/Button.jsx": {"language": "JavaScript JSX", "code": 64, "comment": 11, "blank": 7}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/components/layout/Layout.jsx": {"language": "JavaScript JSX", "code": 31, "comment": 8, "blank": 8}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/components/layout/Sidebar.jsx": {"language": "JavaScript JSX", "code": 178, "comment": 12, "blank": 14}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/components/modals/EmployerModal.jsx": {"language": "JavaScript JSX", "code": 296, "comment": 11, "blank": 19}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/components/layout/Navbar.jsx": {"language": "JavaScript JSX", "code": 199, "comment": 11, "blank": 14}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/components/auth/LoginForm.jsx": {"language": "JavaScript JSX", "code": 171, "comment": 16, "blank": 20}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/components/auth/LoginForm.test.js": {"language": "JavaScript", "code": 118, "comment": 3, "blank": 28}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/components/auth/ProtectedRoute.jsx": {"language": "JavaScript JSX", "code": 41, "comment": 7, "blank": 7}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/package-lock.json": {"language": "JSON", "code": 17963, "comment": 0, "blank": 1}, "file:///c%3A/PFE_PROJECT/ReactJs/omar_pfe/omar_cnss/src/logo.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}}