# Frontend Environment Variables

# API Configuration
REACT_APP_API_URL=http://localhost:8000/api/v1
REACT_APP_API_TIMEOUT=10000

# Application Configuration
REACT_APP_NAME=CNSS Platform
REACT_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=development

# Security Configuration
REACT_APP_ENABLE_HTTPS=false
REACT_APP_COOKIE_SECURE=false

# Features Flags
REACT_APP_ENABLE_DARK_MODE=true
REACT_APP_ENABLE_NOTIFICATIONS=true
REACT_APP_ENABLE_ANALYTICS=false

# External Services
REACT_APP_SENTRY_DSN=
REACT_APP_GOOGLE_ANALYTICS_ID=

# Development Configuration
REACT_APP_DEBUG=true
REACT_APP_LOG_LEVEL=debug
