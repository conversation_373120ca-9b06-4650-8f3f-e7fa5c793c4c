# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
node_modules/
*/node_modules/
/.pnp
.pnp.js

# Testing
/coverage
*.lcov
.nyc_output

# Production builds
/build
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
backend/.env

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
*.log

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/

# Distribution / packaging
.Python
develop-eggs/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Database
*.db
*.sqlite3

# Docker
.dockerignore

# Uploads
uploads/
backend/uploads/

# SSL certificates
*.pem
*.key
*.crt
ssl/

# Backup files
*.bak
*.backup

# IDE
.vscode/settings.json
.idea/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini
*~
