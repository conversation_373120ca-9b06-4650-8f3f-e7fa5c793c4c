# 🗄️ Oracle Database Setup Guide for CNSS Platform

## ❌ **Problem**: SQL*Plus Not Found

The error `'sqlplus' is not recognized as an internal or external command` means Oracle client tools are not installed or not in your system PATH.

## ✅ **Solution Options**

### **Option 1: Install Oracle Instant Client (Recommended)**

1. **Download Oracle Instant Client**:
   - Go to: https://www.oracle.com/database/technologies/instant-client/downloads.html
   - Download "Basic Package" for Windows x64
   - Download "SQL*Plus Package" for Windows x64

2. **Install**:
   - Extract both ZIP files to `C:\oracle\instantclient_21_12\`
   - Add `C:\oracle\instantclient_21_12` to your system PATH

3. **Test Installation**:
   ```cmd
   sqlplus /nolog
   ```

### **Option 2: Use Oracle Database Express Edition (XE)**

1. **Download Oracle XE**:
   - Go to: https://www.oracle.com/database/technologies/xe-downloads.html
   - Download Oracle Database 21c Express Edition

2. **Install Oracle XE**:
   - Run the installer
   - Set password for SYS and SYSTEM users
   - Default port: 1521
   - Default service: XEPDB1

3. **Test Connection**:
   ```cmd
   sqlplus sys/your_password@localhost:1521/XE as sysdba
   ```

### **Option 3: Use Docker (Easiest)**

1. **Install Docker Desktop**
2. **Run Oracle in Docker**:
   ```cmd
   docker run -d --name oracle-xe -p 1521:1521 -p 5500:5500 -e ORACLE_PWD=password123 gvenzl/oracle-xe:21-slim
   ```

3. **Connect**:
   ```cmd
   docker exec -it oracle-xe sqlplus sys/password123@XEPDB1 as sysdba
   ```

## 🔧 **Setup CNSS Database**

### **Step 1: Create User and Tablespace**

```sql
-- Connect as SYS
sqlplus sys/your_password@localhost:1521/XEPDB1 as sysdba

-- Create tablespace
CREATE TABLESPACE cnss_data
DATAFILE 'cnss_data.dbf' SIZE 100M
AUTOEXTEND ON NEXT 10M MAXSIZE 1G;

-- Create user
CREATE USER cnss_user IDENTIFIED BY cnss_password
DEFAULT TABLESPACE cnss_data
TEMPORARY TABLESPACE temp;

-- Grant privileges
GRANT CONNECT, RESOURCE, CREATE VIEW TO cnss_user;
GRANT UNLIMITED TABLESPACE TO cnss_user;
```

### **Step 2: Create Tables**

```sql
-- Connect as cnss_user
sqlplus cnss_user/cnss_password@localhost:1521/XEPDB1

-- Create EMPLOYEUR table
CREATE TABLE employeur (
    emp_mat NUMBER(8) NOT NULL,
    emp_cle NUMBER(2) NOT NULL,
    emp_rais VARCHAR2(100),
    emp_sigle VARCHAR2(20),
    emp_activite VARCHAR2(100),
    emp_email VARCHAR2(100),
    emp_tel VARCHAR2(20),
    emp_dtaff DATE,
    emp_dtdact DATE,
    CONSTRAINT pk_employeur PRIMARY KEY (emp_mat, emp_cle)
);

-- Create ASSURE table
CREATE TABLE assure (
    ass_mat NUMBER(8) NOT NULL,
    ass_cle NUMBER(2) NOT NULL,
    emp_mat NUMBER(8),
    emp_cle NUMBER(2),
    ass_dteff DATE,
    ass_dtimmat DATE,
    ass_rib VARCHAR2(24),
    ass_cnrps VARCHAR2(20),
    etat_chargement VARCHAR2(20),
    CONSTRAINT pk_assure PRIMARY KEY (ass_mat, ass_cle),
    CONSTRAINT fk_assure_employeur FOREIGN KEY (emp_mat, emp_cle) 
        REFERENCES employeur(emp_mat, emp_cle)
);

-- Create BENEFICIAIRE table
CREATE TABLE beneficiaire (
    ben_mat NUMBER(8) NOT NULL,
    ben_cle NUMBER(2) NOT NULL,
    ben_nom VARCHAR2(50),
    ben_prenom VARCHAR2(50),
    ben_email VARCHAR2(100),
    ben_tel VARCHAR2(20),
    ben_cin VARCHAR2(8),
    ben_dtnaiss DATE,
    ben_dtdeces DATE,
    CONSTRAINT pk_beneficiaire PRIMARY KEY (ben_mat, ben_cle)
);
```

### **Step 3: Insert Sample Data**

```sql
-- Insert sample employers
INSERT INTO employeur VALUES (12345678, 12, 'SOCIETE GENERALE TUNISIE', 'SGT', 'BANQUE', '<EMAIL>', '71123456', DATE '2020-01-15', NULL);
INSERT INTO employeur VALUES (87654321, 34, 'TUNISIE TELECOM', 'TT', 'TELECOMMUNICATIONS', '<EMAIL>', '71987654', DATE '2019-03-20', NULL);

-- Insert sample assured persons
INSERT INTO assure VALUES (11111111, 11, 12345678, 12, DATE '2020-02-01', DATE '2020-01-20', 'TN59 1234 5678 9012 3456 7890', 'CNRPS123', 'ACTIF');
INSERT INTO assure VALUES (22222222, 22, 87654321, 34, DATE '2019-04-01', DATE '2019-03-25', 'TN59 9876 5432 1098 7654 3210', 'CNRPS456', 'ACTIF');

-- Insert sample beneficiaries
INSERT INTO beneficiaire VALUES (33333333, 33, 'BEN ALI', 'MOHAMED', '<EMAIL>', '98123456', '12345678', DATE '1985-05-15', NULL);
INSERT INTO beneficiaire VALUES (44444444, 44, 'TRABELSI', 'FATMA', '<EMAIL>', '97987654', '87654321', DATE '1990-08-22', NULL);

COMMIT;
```

## 🔗 **Update Backend Configuration**

Update your FastAPI backend configuration:

```python
# database.py
import cx_Oracle

DATABASE_URL = "oracle+cx_oracle://cnss_user:cnss_password@localhost:1521/XEPDB1"

# Or for direct connection
def get_oracle_connection():
    return cx_Oracle.connect(
        user="cnss_user",
        password="cnss_password",
        dsn="localhost:1521/XEPDB1"
    )
```

## 🧪 **Test Connection**

```python
# test_connection.py
import cx_Oracle

try:
    connection = cx_Oracle.connect(
        user="cnss_user",
        password="cnss_password", 
        dsn="localhost:1521/XEPDB1"
    )
    
    cursor = connection.cursor()
    cursor.execute("SELECT COUNT(*) FROM employeur")
    result = cursor.fetchone()
    print(f"Employeurs count: {result[0]}")
    
    connection.close()
    print("✅ Oracle connection successful!")
    
except Exception as e:
    print(f"❌ Connection failed: {e}")
```

## 📝 **Connection String Examples**

### **For SQLAlchemy (FastAPI)**:
```python
DATABASE_URL = "oracle+cx_oracle://cnss_user:cnss_password@localhost:1521/XEPDB1"
```

### **For Direct Connection**:
```python
dsn = cx_Oracle.makedsn("localhost", 1521, service_name="XEPDB1")
connection = cx_Oracle.connect("cnss_user", "cnss_password", dsn)
```

### **For SQL*Plus**:
```cmd
sqlplus cnss_user/cnss_password@localhost:1521/XEPDB1
```

## 🚀 **Quick Start Commands**

1. **Install Oracle XE or Instant Client**
2. **Create database**:
   ```cmd
   sqlplus sys/password@localhost:1521/XEPDB1 as sysdba
   @database/01_create_user.sql
   ```
3. **Create tables**:
   ```cmd
   sqlplus cnss_user/cnss_password@localhost:1521/XEPDB1
   @database/02_create_tables.sql
   ```
4. **Insert data**:
   ```cmd
   sqlplus cnss_user/cnss_password@localhost:1521/XEPDB1
   @database/03_insert_data.sql
   ```

## 🎯 **Next Steps**

1. **Choose one installation option above**
2. **Follow the setup steps**
3. **Test the connection**
4. **Update your FastAPI backend configuration**
5. **Start your backend server**
6. **Test the frontend with real data**

Your CNSS platform will then be **fully functional** with Oracle database integration! 🎉
