# 🚀 Quick Start Guide - CNSS Platform

## ⚡ Fast Setup (5 minutes)

### 1. Start the Backend Server

```bash
# Navigate to backend directory
cd backend

# Install dependencies (if not done already)
pip install -r requirements.txt

# Start the FastAPI server
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8001
```

The backend will be available at: `http://localhost:8001`
API documentation: `http://localhost:8001/docs`

### 2. Start the Frontend

```bash
# In a new terminal, navigate to project root
cd ..

# Install dependencies (if not done already)
npm install

# Start the React development server
npm start
```

The frontend will be available at: `http://localhost:3000`

### 3. Login Credentials

```
Email: <EMAIL>
Password: admin123
```

## 🔧 If You Get API Errors

The error messages you're seeing ("Erreur lors du chargement des employeurs") happen because:

1. **Backend not running**: Make sure the FastAPI server is running on port 8001
2. **Oracle database not connected**: The backend needs Oracle 23c database
3. **Tables don't exist**: Run the SQL script to create the tables

### Quick Fix for Testing

If you want to test the UI without Oracle database:

1. **Mock Data Mode**: The frontend will show better error messages now
2. **Check Console**: Open browser console (F12) to see detailed error messages
3. **Backend Status**: Visit `http://localhost:8001/docs` to check if backend is running

## 🗄️ Database Setup (Optional for UI Testing)

If you want real data:

1. **Install Oracle 23c** or use Oracle XE
2. **Run the SQL script**: `database/oracle_setup.sql`
3. **Update connection**: Edit `backend/app/core/config.py` with your Oracle credentials

## 🎨 New Features Added

### ✅ Fixed Issues:
- **Better Error Messages**: More helpful error messages when backend is not available
- **Mobile Sidebar**: Close button now works properly on mobile devices
- **Settings Page**: Added to sidebar with theme controls (dark/light mode)
- **User Menu**: Removed "Paramètres" from header menu (now in sidebar)
- **Grey Square**: Removed the weird grey square at bottom of sidebar

### ✅ New Features:
- **Dark Mode**: Full dark mode support across all pages
- **Theme Settings**: Light, Dark, and System theme options
- **Font Size**: Adjustable text size (Small, Medium, Large)
- **Responsive Design**: Better mobile experience

## 🎯 Pages Available

1. **Dashboard** (`/dashboard`) - Overview with statistics
2. **Employeurs** (`/employeurs`) - Employers management
3. **Assurés** (`/assures`) - Insured persons management  
4. **Bénéficiaires** (`/beneficiaires`) - Beneficiaries management
5. **Paramètres** (`/settings`) - Theme and system settings

## 🔍 Troubleshooting

### Common Issues:

1. **"Impossible de se connecter au serveur"**
   - Backend is not running
   - Start with: `python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8001`

2. **"Service non disponible"**
   - Backend is running but Oracle database is not connected
   - Check Oracle service is running
   - Verify connection settings in `backend/app/core/config.py`

3. **Mobile sidebar won't close**
   - This is now fixed! The close button should work properly

4. **Dark mode not working**
   - Go to Settings page (in sidebar)
   - Choose your preferred theme
   - Changes apply immediately

### Check Backend Status:
```bash
# Test if backend is running
curl http://localhost:8001/docs

# Or visit in browser:
http://localhost:8001/docs
```

### Check Frontend Status:
```bash
# Frontend should be running on:
http://localhost:3000
```

## 📱 Mobile Experience

- **Responsive Design**: Works on all screen sizes
- **Mobile Menu**: Hamburger menu for navigation
- **Touch Friendly**: Optimized for touch interactions
- **Close Button**: Working close button for sidebar on mobile

## 🌙 Dark Mode

- **Auto Detection**: Follows system preference by default
- **Manual Control**: Override in Settings page
- **Persistent**: Remembers your choice
- **Full Coverage**: All pages support dark mode

## 🎨 Theme Customization

Visit the **Settings** page to customize:
- **Theme**: Light, Dark, or System
- **Font Size**: Small, Medium, or Large
- **Preview**: See changes in real-time

## 📞 Need Help?

1. **Check browser console** (F12) for detailed error messages
2. **Verify backend is running** at `http://localhost:8001/docs`
3. **Check network tab** in browser dev tools for API call failures
4. **Review error messages** - they now provide more specific guidance

## 🎉 Ready to Go!

Your CNSS platform is now ready with:
- ✅ Modern, responsive UI
- ✅ Dark/Light theme support
- ✅ Mobile-friendly design
- ✅ Better error handling
- ✅ Professional appearance
- ✅ Oracle database integration (when configured)

Start the backend and frontend servers, then navigate to `http://localhost:3000` to begin!
