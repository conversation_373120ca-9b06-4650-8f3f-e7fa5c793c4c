# 🏛️ CNSS Data Exchange Platform

> **Production-Ready CNSS Platform for Tunisia**
> A modern, secure, and scalable web platform for the Caisse Nationale de Sécurité Sociale (CNSS) in Tunisia to exchange APIs with partner organizations and manage employer/service data.

![React](https://img.shields.io/badge/React-19.1.0-blue?logo=react)
![FastAPI](https://img.shields.io/badge/FastAPI-0.104.1-green?logo=fastapi)
![Oracle](https://img.shields.io/badge/Oracle-23c-red?logo=oracle)
![Docker](https://img.shields.io/badge/Docker-Enabled-blue?logo=docker)
![TypeScript](https://img.shields.io/badge/TypeScript-Ready-blue?logo=typescript)

---

## 🎯 **Project Overview**

This is a **final-year high school project** that demonstrates **real-world**, **production-ready** software development skills. The platform provides:

- 🔐 **Secure Authentication** with JWT tokens
- 🏢 **Employer Management** system
- ⚙️ **Service Configuration** and monitoring
- 🔄 **API Exchange** capabilities with partner organizations
- 👥 **User Management** with role-based access control
- 📊 **Dashboard** with real-time statistics
- 🐳 **Docker** containerization for easy deployment

---

## 🏗️ **Architecture**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Database      │
│   (React)       │◄──►│   (FastAPI)     │◄──►│  (Oracle 23c)   │
│   Port: 3000    │    │   Port: 8000    │    │   Port: 1521    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │     Redis       │
                    │   (Caching)     │
                    │   Port: 6379    │
                    └─────────────────┘
```

---

## 🚀 **Quick Start**

### **Prerequisites**
- Node.js 18+ and npm
- Python 3.11+
- Docker and Docker Compose
- Oracle Database 23c (or Docker container)

### **1. Clone the Repository**
```bash
git clone <repository-url>
cd omar_cnss
```

### **2. Frontend Setup**
```bash
# Install dependencies
npm install

# Start development server
npm start
```
The frontend will be available at `http://localhost:3001`

### **3. Backend Setup**
```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Copy environment file
cp .env.example .env
# Edit .env with your database credentials

# Start the server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```
The API will be available at `http://localhost:8000`

### **4. Docker Setup (Recommended)**
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

---

## 🎨 **Design System**

The platform features a **modern, soft UI design** with:

- ✨ **Rounded corners** for all elements
- 🌊 **Soft drop shadows** for a floating feel
- 🎨 **Pastel color palette** with professional tones
- 📱 **Fully responsive** design (desktop, tablet, mobile)
- 🔄 **Smooth animations** and transitions
- ♿ **Accessibility-first** approach

### **Color Palette**
- **Primary**: Blue tones (#0ea5e9 to #0c4a6e)
- **Secondary**: Gray tones (#f8fafc to #0f172a)
- **Success**: Green tones (#22c55e to #14532d)
- **Warning**: Orange tones (#f59e0b to #78350f)
- **Error**: Red tones (#ef4444 to #7f1d1d)

---

## 📁 **Project Structure**

```
omar_cnss/
├── 📁 src/                          # Frontend source code
│   ├── 📁 components/               # Reusable UI components
│   │   ├── 📁 ui/                   # Base UI components
│   │   ├── 📁 auth/                 # Authentication components
│   │   └── 📁 layout/               # Layout components
│   ├── 📁 contexts/                 # React contexts
│   ├── 📁 pages/                    # Page components
│   ├── 📁 services/                 # API services
│   └── 📁 utils/                    # Utility functions
├── 📁 backend/                      # Backend source code
│   ├── 📁 app/                      # FastAPI application
│   │   ├── 📁 api/                  # API routes
│   │   ├── 📁 core/                 # Core functionality
│   │   ├── 📁 models/               # Database models
│   │   └── 📁 schemas/              # Pydantic schemas
│   └── 📄 requirements.txt          # Python dependencies
├── 📁 database/                     # Database scripts
├── 📄 docker-compose.yml            # Docker services
├── 📄 Dockerfile                    # Frontend Docker image
└── 📄 README.md                     # This file
```

---

## 🔧 **Technologies Used**

### **Frontend**
- **React 19.1.0** - Modern UI library
- **React Router** - Client-side routing
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Smooth animations
- **Axios** - HTTP client
- **React Hot Toast** - Notifications
- **Heroicons** - Beautiful icons

### **Backend**
- **FastAPI** - Modern Python web framework
- **SQLAlchemy** - ORM for database operations
- **Pydantic** - Data validation and serialization
- **JWT** - Secure authentication
- **Uvicorn** - ASGI server
- **Alembic** - Database migrations

### **Database**
- **Oracle Database 23c** - Enterprise database
- **Redis** - Caching and session storage

### **DevOps**
- **Docker** - Containerization
- **Docker Compose** - Multi-container orchestration
- **Nginx** - Reverse proxy and load balancer

---

## 🔐 **Security Features**

- 🔒 **JWT Authentication** with access and refresh tokens
- 🛡️ **Role-based Access Control** (Admin, Agent)
- 🔐 **Password Hashing** with bcrypt
- 🌐 **CORS Protection** with configurable origins
- 🛡️ **Input Validation** with Pydantic schemas
- 🔒 **SQL Injection Protection** with SQLAlchemy ORM
- 🔐 **XSS Protection** with security headers
- 🛡️ **Rate Limiting** for API endpoints

---

## 📊 **Features**

### **🔐 Authentication System**
- Secure login/logout functionality
- JWT token-based authentication
- Password reset capability
- Session management

### **👥 User Management**
- Role-based access control
- User creation and management
- Profile management
- Activity tracking

### **🏢 Employer Management**
- Company registration and verification
- Contact information management
- Business details tracking
- Document management

### **⚙️ Service Configuration**
- API service definitions
- Endpoint configuration
- Rate limiting settings
- Documentation management

### **🔄 API Exchange**
- Partner organization integration
- Real-time data exchange
- Transaction logging
- Error handling and retry logic

### **📊 Dashboard & Analytics**
- Real-time statistics
- System health monitoring
- Activity logs
- Performance metrics

---

## 🚀 **Development**

### **Frontend Development**
```bash
# Start development server
npm start

# Run tests
npm test

# Build for production
npm run build

# Lint code
npm run lint
```

### **Backend Development**
```bash
# Start development server
uvicorn app.main:app --reload

# Run tests
pytest

# Format code
black app/
isort app/

# Type checking
mypy app/
```

### **Database Management**
```bash
# Create migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1
```

---

## 🐳 **Docker Deployment**

### **Development Environment**
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f backend
docker-compose logs -f frontend

# Rebuild services
docker-compose up -d --build

# Stop services
docker-compose down
```

### **Production Environment**
```bash
# Start with production profile
docker-compose --profile production up -d

# Scale services
docker-compose up -d --scale backend=3

# Update services
docker-compose pull
docker-compose up -d
```

---

## 🧪 **Testing**

### **Frontend Testing**
```bash
# Run all tests
npm test

# Run tests with coverage
npm test -- --coverage

# Run specific test file
npm test -- LoginForm.test.js
```

### **Backend Testing**
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test
pytest tests/test_auth.py
```

---

## 📝 **API Documentation**

The API documentation is automatically generated and available at:
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

### **Authentication Endpoints**
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh token
- `POST /api/v1/auth/logout` - User logout
- `GET /api/v1/auth/me` - Get current user

### **User Management Endpoints**
- `GET /api/v1/users` - List users
- `POST /api/v1/users` - Create user
- `GET /api/v1/users/{id}` - Get user
- `PUT /api/v1/users/{id}` - Update user
- `DELETE /api/v1/users/{id}` - Delete user

---

## 🔧 **Configuration**

### **Environment Variables**

#### **Frontend (.env)**
```env
REACT_APP_API_URL=http://localhost:8000/api/v1
REACT_APP_ENVIRONMENT=development
REACT_APP_DEBUG=true
```

#### **Backend (.env)**
```env
DATABASE_URL=oracle://user:password@localhost:1521/XE
SECRET_KEY=your-super-secret-key
DEBUG=True
ENVIRONMENT=development
```

---

## 🚀 **Deployment**

### **Production Checklist**
- [ ] Update environment variables
- [ ] Configure SSL certificates
- [ ] Set up database backups
- [ ] Configure monitoring
- [ ] Set up logging
- [ ] Configure firewall rules
- [ ] Test all endpoints
- [ ] Perform security audit

### **Deployment Steps**
1. **Prepare Environment**
   ```bash
   # Clone repository
   git clone <repository-url>
   cd omar_cnss

   # Copy production environment files
   cp .env.example .env
   cp backend/.env.example backend/.env
   ```

2. **Configure Database**
   ```bash
   # Set up Oracle Database
   # Update connection strings in .env files
   ```

3. **Deploy with Docker**
   ```bash
   # Build and start services
   docker-compose --profile production up -d

   # Verify deployment
   docker-compose ps
   docker-compose logs
   ```

---

## 🤝 **Contributing**

This is a final-year high school project, but contributions and suggestions are welcome!

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

---

## 📄 **License**

This project is created for educational purposes as a final-year high school project.

---

## 👨‍💻 **Author**

**Omar** - Final Year High School Student
*Building production-ready software for CNSS Tunisia*

---

## 🙏 **Acknowledgments**

- **CNSS Tunisia** for the project inspiration
- **React Team** for the amazing frontend framework
- **FastAPI Team** for the modern Python web framework
- **Oracle** for the enterprise database solution
- **Tailwind CSS** for the beautiful design system

---

## 📞 **Support**

If you have any questions or need help with the project:

1. Check the [API Documentation](http://localhost:8000/docs)
2. Review the [Project Structure](#-project-structure)
3. Look at the [Configuration](#-configuration) section
4. Check the [Troubleshooting](#-troubleshooting) guide

---

## 🔍 **Troubleshooting**

### **Common Issues**

#### **Frontend Issues**
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Check for port conflicts
lsof -i :3000
```

#### **Backend Issues**
```bash
# Check Python version
python --version

# Reinstall dependencies
pip install -r requirements.txt --force-reinstall

# Check database connection
python -c "from app.core.database import engine; print(engine.execute('SELECT 1').scalar())"
```

#### **Docker Issues**
```bash
# Clean Docker system
docker system prune -a

# Rebuild containers
docker-compose down
docker-compose up -d --build

# Check container logs
docker-compose logs backend
```

---

**🎓 This project demonstrates real-world software development skills and is ready for production deployment in a government organization environment.**
