# 🏗️ CNSS Platform Setup Instructions

## 📋 Overview

This guide will help you set up the complete CNSS platform with:
- **Frontend**: React.js with modern UI components
- **Backend**: FastAPI with Oracle database integration
- **Database**: Oracle 23c with three main tables (employeur, assure, beneficiaire)

## 🗄️ Database Setup

### Step 1: Oracle Database Setup

1. **Create Oracle Database Tables**
   ```bash
   # Connect to your Oracle 23c database
   sqlplus cnss_user/cnss_password@localhost:1521/XEPDB1
   
   # Run the setup script
   @database/oracle_setup.sql
   ```

2. **Verify Tables Creation**
   ```sql
   -- Check if tables exist
   SELECT table_name FROM user_tables WHERE table_name IN ('EMPLOYEUR', 'ASSURE', 'BENEFICIAIRE');
   
   -- Check sample data
   SELECT COUNT(*) FROM employeur;
   SELECT COUNT(*) FROM assure;
   SELECT COUNT(*) FROM beneficiaire;
   ```

### Step 2: Database Configuration

Update the database connection settings in `backend/app/core/config.py`:

```python
# Database settings
DB_HOST: str = "localhost"          # Your Oracle host
DB_PORT: int = 1521                 # Your Oracle port
DB_USER: str = "cnss_user"          # Your Oracle username
DB_PASSWORD: str = "cnss_password"  # Your Oracle password
DB_SERVICE_NAME: str = "XEPDB1"     # Your Oracle service name
```

## 🔧 Backend Setup

### Step 1: Install Dependencies

```bash
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### Step 2: Install Oracle Client

**For Windows:**
1. Download Oracle Instant Client from Oracle website
2. Extract to `C:\oracle\instantclient_21_x`
3. Add to PATH environment variable

**For Linux:**
```bash
# Ubuntu/Debian
sudo apt-get install libaio1
wget https://download.oracle.com/otn_software/linux/instantclient/213000/instantclient-basic-linux.x64-********.0.zip
unzip instantclient-basic-linux.x64-********.0.zip
sudo mv instantclient_21_3 /opt/oracle/
echo 'export LD_LIBRARY_PATH=/opt/oracle/instantclient_21_3:$LD_LIBRARY_PATH' >> ~/.bashrc
source ~/.bashrc
```

### Step 3: Start Backend Server

```bash
# From backend directory
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8001
```

The backend will be available at: `http://localhost:8001`
API documentation: `http://localhost:8001/docs`

## 🎨 Frontend Setup

### Step 1: Install Dependencies

```bash
# From project root
npm install
```

### Step 2: Configure API URL

Update the API URL in `src/services/api.js` if needed:

```javascript
const API_BASE_URL = process.env.REACT_APP_API_URL || "http://localhost:8001/api/v1";
```

### Step 3: Start Frontend Server

```bash
npm start
```

The frontend will be available at: `http://localhost:3000`

## 🐳 Docker Setup (Optional)

### Step 1: Build and Run with Docker Compose

```bash
# Build and start all services
docker-compose up --build

# Run in background
docker-compose up -d --build
```

### Step 2: Access Services

- **Frontend**: `http://localhost:3000`
- **Backend**: `http://localhost:8001`
- **API Docs**: `http://localhost:8001/docs`

## 🔐 Authentication

### Default Login Credentials

```
Email: <EMAIL>
Password: admin123
```

## 📊 Available Features

### 1. Dashboard
- Real-time statistics from Oracle database
- Overview of employeurs, assurés, and bénéficiaires
- System health monitoring

### 2. Employeurs Management
- List all employers with pagination
- Search by company name, email, activity
- View employer details and statistics
- Filter active/inactive employers

### 3. Assurés Management
- List all insured persons
- Filter by employer
- View insurance details and dates
- Statistics on RIB and CNRPS coverage

### 4. Bénéficiaires Management
- List all beneficiaries
- Search by name, ID, email
- Filter by beneficiary type
- Gender and contact statistics

## 🔧 API Endpoints

### Employeurs
- `GET /api/v1/employeurs/` - List employers
- `GET /api/v1/employeurs/count` - Count employers
- `GET /api/v1/employeurs/statistics` - Employer statistics
- `GET /api/v1/employeurs/{emp_mat}/{emp_cle}` - Get specific employer

### Assurés
- `GET /api/v1/assures/` - List insured persons
- `GET /api/v1/assures/count` - Count insured persons
- `GET /api/v1/assures/statistics` - Insured statistics
- `GET /api/v1/assures/{ass_mat}/{ass_cle}` - Get specific insured person

### Bénéficiaires
- `GET /api/v1/beneficiaires/` - List beneficiaries
- `GET /api/v1/beneficiaires/count` - Count beneficiaries
- `GET /api/v1/beneficiaires/statistics` - Beneficiary statistics
- `GET /api/v1/beneficiaires/{ben_iducnss}` - Get specific beneficiary

## 🚀 Production Deployment

### Environment Variables

Create `.env` file in backend directory:

```env
# Database
DB_HOST=your_oracle_host
DB_PORT=1521
DB_USER=your_oracle_user
DB_PASSWORD=your_oracle_password
DB_SERVICE_NAME=your_service_name

# Security
SECRET_KEY=your_super_secret_key_here
ALGORITHM=HS256

# CORS
ALLOWED_ORIGINS=["https://your-domain.com"]

# Environment
ENVIRONMENT=production
DEBUG=false
```

### Build for Production

```bash
# Frontend
npm run build

# Backend
pip install gunicorn
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8001
```

## 🔍 Troubleshooting

### Common Issues

1. **Oracle Connection Error**
   - Check Oracle service is running
   - Verify connection parameters
   - Ensure Oracle client is installed

2. **Backend Import Errors**
   - Activate virtual environment
   - Install all requirements: `pip install -r requirements.txt`

3. **Frontend API Errors**
   - Check backend is running on port 8001
   - Verify API_BASE_URL in `src/services/api.js`

4. **CORS Issues**
   - Update ALLOWED_ORIGINS in backend config
   - Check frontend URL is allowed

### Logs

- **Backend logs**: Check console output when running uvicorn
- **Frontend logs**: Check browser console (F12)
- **Database logs**: Check Oracle alert logs

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review API documentation at `/docs`
3. Check database connection and table structure
4. Verify all dependencies are installed correctly

## 🎯 Next Steps

After successful setup:
1. Add more sample data to test functionality
2. Customize UI components and styling
3. Add additional business logic as needed
4. Set up monitoring and logging for production
5. Configure backup and recovery procedures
