-- ========================================
-- Add Sample Data to Your Existing Tables
-- ========================================
-- Run this as cnss_user to add sample data

-- Connect as cnss_user (if not already connected)
-- CONNECT cnss_user/cnss_password@XEPDB1;

-- Add sample ASSURE data (your table has 0 records)
INSERT INTO assure (ass_mat, ass_cle, emp_mat, emp_cle, ass_dteff, ass_dtimmat, ass_rib, ass_cnrps, etat_chargement) VALUES
(11111111, 11, 12345678, 12, DATE '2020-02-01', DATE '2020-01-20', 'TN59 1234 5678 9012 3456 7890', 'CNRPS123', 'ACTIF');

INSERT INTO assure (ass_mat, ass_cle, emp_mat, emp_cle, ass_dteff, ass_dtimmat, ass_rib, ass_cnrps, etat_chargement) VALUES
(22222222, 22, 87654321, 34, DATE '2019-04-01', DATE '2019-03-25', 'TN59 9876 5432 1098 7654 3210', 'CNRPS456', 'ACTIF');

INSERT INTO assure (ass_mat, ass_cle, emp_mat, emp_cle, ass_dteff, ass_dtimmat, ass_rib, ass_cnrps, etat_chargement) VALUES
(33333333, 33, 11223344, 56, DATE '2018-07-15', DATE '2018-06-20', 'TN59 1111 2222 3333 4444 5555', 'CNRPS789', 'ACTIF');

INSERT INTO assure (ass_mat, ass_cle, emp_mat, emp_cle, ass_dteff, ass_dtimmat, ass_rib, ass_cnrps, etat_chargement) VALUES
(44444444, 44, 99887766, 78, DATE '2015-02-01', DATE '2015-01-15', 'TN59 6666 7777 8888 9999 0000', 'CNRPS012', 'ACTIF');

-- Commit the changes
COMMIT;

-- Verify the data
SELECT 'EMPLOYEUR' as TABLE_NAME, COUNT(*) as RECORD_COUNT FROM employeur
UNION ALL
SELECT 'ASSURE' as TABLE_NAME, COUNT(*) as RECORD_COUNT FROM assure
UNION ALL
SELECT 'BENEFICIAIRE' as TABLE_NAME, COUNT(*) as RECORD_COUNT FROM beneficiaire;

PROMPT
PROMPT ========================================
PROMPT ✅ Sample Data Added Successfully!
PROMPT ========================================
PROMPT
PROMPT All tables now have sample data
PROMPT You can now test the backend connection
PROMPT ========================================
