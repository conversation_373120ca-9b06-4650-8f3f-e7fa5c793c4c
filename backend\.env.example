# Database Configuration
DATABASE_URL=oracle://username:password@localhost:1521/XEPDB1
DB_HOST=localhost
DB_PORT=1521
DB_USER=cnss_user
DB_PASSWORD=your_password
DB_SERVICE_NAME=XEPDB1

# Application Configuration
APP_NAME=CNSS API Platform
APP_VERSION=1.0.0
DEBUG=True
ENVIRONMENT=development

# Security Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS Configuration
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:3001"]
ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
ALLOWED_HEADERS=["*"]

# API Configuration
API_V1_PREFIX=/api/v1
DOCS_URL=/docs
REDOC_URL=/redoc

# External APIs
EXTERNAL_API_TIMEOUT=30
EXTERNAL_API_RETRY_COUNT=3

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Redis Configuration (for caching and background tasks)
REDIS_URL=redis://localhost:6379/0

# File Upload
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=uploads

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# Monitoring
SENTRY_DSN=
ENABLE_METRICS=True
