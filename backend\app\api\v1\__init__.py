"""
API v1 routes
"""
from fastapi import APIRouter
from app.api.v1 import auth, cnss, employeurs, assures, beneficiaires

api_router = APIRouter()

# Include authentication routes
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])

# Include Oracle database routes
api_router.include_router(employeurs.router, prefix="/employeurs", tags=["employeurs"])
api_router.include_router(assures.router, prefix="/assures", tags=["assures"])
api_router.include_router(beneficiaires.router, prefix="/beneficiaires", tags=["beneficiaires"])

# Include legacy CNSS routes with proper prefixes
api_router.include_router(cnss.router, prefix="/employers", tags=["employers"])
api_router.include_router(cnss.router, prefix="/services", tags=["services"])
api_router.include_router(cnss.router, prefix="/dashboard", tags=["dashboard"])

# Include CNSS routes without prefix for direct access
api_router.include_router(cnss.router, tags=["cnss"])
