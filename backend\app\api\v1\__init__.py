"""
API v1 routes
"""
from fastapi import APIRouter
from app.api.v1 import auth, cnss

api_router = APIRouter()

# Include authentication routes
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])

# Include CNSS routes with proper prefixes
api_router.include_router(cnss.router, prefix="/employers", tags=["employers"])
api_router.include_router(cnss.router, prefix="/services", tags=["services"])
api_router.include_router(cnss.router, prefix="/dashboard", tags=["dashboard"])

# Include CNSS routes without prefix for direct access
api_router.include_router(cnss.router, tags=["cnss"])
