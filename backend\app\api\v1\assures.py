"""
Assures (Insured Persons) API endpoints
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_

from app.core.database import get_db
from app.models.assure import Assure
from app.api.deps import get_current_user

router = APIRouter()

@router.get("/", response_model=List[dict])
async def get_assures(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    emp_mat: Optional[int] = Query(None, description="Filter by employer matricule"),
    db: Session = Depends(get_db)
    # current_user = Depends(get_current_user)  # Temporarily disabled for testing
):
    """
    Get list of insured persons with pagination and filtering
    """
    try:
        query = db.query(Assure)
        
        # Apply employer filter if provided
        if emp_mat:
            query = query.filter(Assure.emp_mat == emp_mat)
        
        # Apply pagination
        assures = query.offset(skip).limit(limit).all()
        
        # Convert to dict for JSON response
        result = [assure.to_dict() for assure in assures]
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching insured persons: {str(e)}")

@router.get("/count")
async def get_assures_count(
    emp_mat: Optional[int] = Query(None, description="Filter by employer matricule"),
    db: Session = Depends(get_db)
    # current_user = Depends(get_current_user)  # Temporarily disabled for testing
):
    """
    Get total count of insured persons
    """
    try:
        query = db.query(Assure)
        
        # Apply employer filter if provided
        if emp_mat:
            query = query.filter(Assure.emp_mat == emp_mat)
        
        count = query.count()
        return {"count": count}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error counting insured persons: {str(e)}")

@router.get("/{ass_mat}/{ass_cle}")
async def get_assure(
    ass_mat: int,
    ass_cle: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get specific insured person by matricule and key
    """
    try:
        assure = db.query(Assure).filter(
            and_(
                Assure.ass_mat == ass_mat,
                Assure.ass_cle == ass_cle
            )
        ).first()
        
        if not assure:
            raise HTTPException(status_code=404, detail="Insured person not found")
        
        return assure.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching insured person: {str(e)}")

@router.get("/by-employer/{emp_mat}")
async def get_assures_by_employer(
    emp_mat: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get insured persons by employer
    """
    try:
        assures = db.query(Assure).filter(
            Assure.emp_mat == emp_mat
        ).offset(skip).limit(limit).all()
        
        result = [assure.to_dict() for assure in assures]
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching insured persons by employer: {str(e)}")

@router.get("/statistics")
async def get_assures_statistics(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get insured persons statistics
    """
    try:
        total_count = db.query(Assure).count()
        
        # Count by status
        with_employer = db.query(Assure).filter(Assure.emp_mat.isnot(None)).count()
        with_rib = db.query(Assure).filter(Assure.ass_rib.isnot(None)).count()
        with_cnrps = db.query(Assure).filter(Assure.ass_cnrps.isnot(None)).count()
        
        # Count by processing status
        with_loading_status = db.query(Assure).filter(Assure.etat_chargement.isnot(None)).count()
        
        return {
            "total": total_count,
            "with_employer": with_employer,
            "with_rib": with_rib,
            "with_cnrps": with_cnrps,
            "with_loading_status": with_loading_status,
            "employer_percentage": round((with_employer / total_count * 100) if total_count > 0 else 0, 2),
            "rib_percentage": round((with_rib / total_count * 100) if total_count > 0 else 0, 2)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching statistics: {str(e)}")
