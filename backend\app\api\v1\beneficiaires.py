"""
Beneficiaires (Beneficiaries) API endpoints
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_

from app.core.database import get_db
from app.models.beneficiaire import Beneficiaire
from app.api.deps import get_current_user

router = APIRouter()

@router.get("/", response_model=List[dict])
async def get_beneficiaires(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search in name, first name, or ID"),
    ass_mat: Optional[int] = Query(None, description="Filter by insured person matricule"),
    ben_type: Optional[int] = Query(None, description="Filter by beneficiary type"),
    db: Session = Depends(get_db)
    # current_user = Depends(get_current_user)  # Temporarily disabled for testing
):
    """
    Get list of beneficiaries with pagination, search and filtering
    """
    try:
        query = db.query(Beneficiaire)
        
        # Apply search filter if provided
        if search:
            search_filter = or_(
                Beneficiaire.ben_nom.ilike(f"%{search}%"),
                Beneficiaire.ben_nom_ar.ilike(f"%{search}%"),
                Beneficiaire.ben_prenom.ilike(f"%{search}%"),
                Beneficiaire.ben_prn_ar.ilike(f"%{search}%"),
                Beneficiaire.ben_numid.ilike(f"%{search}%"),
                Beneficiaire.ben_email.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)
        
        # Apply insured person filter if provided
        if ass_mat:
            query = query.filter(Beneficiaire.ass_mat == ass_mat)
            
        # Apply beneficiary type filter if provided
        if ben_type:
            query = query.filter(Beneficiaire.ben_type == ben_type)
        
        # Apply pagination
        beneficiaires = query.offset(skip).limit(limit).all()
        
        # Convert to dict for JSON response
        result = [ben.to_dict() for ben in beneficiaires]
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching beneficiaries: {str(e)}")

@router.get("/count")
async def get_beneficiaires_count(
    search: Optional[str] = Query(None, description="Search filter"),
    ass_mat: Optional[int] = Query(None, description="Filter by insured person matricule"),
    ben_type: Optional[int] = Query(None, description="Filter by beneficiary type"),
    db: Session = Depends(get_db)
    # current_user = Depends(get_current_user)  # Temporarily disabled for testing
):
    """
    Get total count of beneficiaries
    """
    try:
        query = db.query(Beneficiaire)
        
        # Apply search filter if provided
        if search:
            search_filter = or_(
                Beneficiaire.ben_nom.ilike(f"%{search}%"),
                Beneficiaire.ben_nom_ar.ilike(f"%{search}%"),
                Beneficiaire.ben_prenom.ilike(f"%{search}%"),
                Beneficiaire.ben_prn_ar.ilike(f"%{search}%"),
                Beneficiaire.ben_numid.ilike(f"%{search}%"),
                Beneficiaire.ben_email.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)
        
        # Apply insured person filter if provided
        if ass_mat:
            query = query.filter(Beneficiaire.ass_mat == ass_mat)
            
        # Apply beneficiary type filter if provided
        if ben_type:
            query = query.filter(Beneficiaire.ben_type == ben_type)
        
        count = query.count()
        return {"count": count}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error counting beneficiaries: {str(e)}")

@router.get("/{ben_iducnss}")
async def get_beneficiaire(
    ben_iducnss: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get specific beneficiary by ID
    """
    try:
        beneficiaire = db.query(Beneficiaire).filter(
            Beneficiaire.ben_iducnss == ben_iducnss
        ).first()
        
        if not beneficiaire:
            raise HTTPException(status_code=404, detail="Beneficiary not found")
        
        return beneficiaire.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching beneficiary: {str(e)}")

@router.get("/by-assure/{ass_mat}")
async def get_beneficiaires_by_assure(
    ass_mat: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get beneficiaries by insured person
    """
    try:
        beneficiaires = db.query(Beneficiaire).filter(
            Beneficiaire.ass_mat == ass_mat
        ).offset(skip).limit(limit).all()
        
        result = [ben.to_dict() for ben in beneficiaires]
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching beneficiaries by insured person: {str(e)}")

@router.get("/statistics")
async def get_beneficiaires_statistics(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get beneficiaries statistics
    """
    try:
        total_count = db.query(Beneficiaire).count()
        
        # Count by gender
        male_count = db.query(Beneficiaire).filter(Beneficiaire.ben_sexe == 1).count()
        female_count = db.query(Beneficiaire).filter(Beneficiaire.ben_sexe == 2).count()
        
        # Count by beneficiary type
        type_1_count = db.query(Beneficiaire).filter(Beneficiaire.ben_type == 1).count()
        type_2_count = db.query(Beneficiaire).filter(Beneficiaire.ben_type == 2).count()
        
        # Count with contact info
        with_email = db.query(Beneficiaire).filter(Beneficiaire.ben_email.isnot(None)).count()
        with_phone = db.query(Beneficiaire).filter(Beneficiaire.ben_tel.isnot(None)).count()
        with_address = db.query(Beneficiaire).filter(Beneficiaire.ben_adr.isnot(None)).count()
        
        # Count by ID type
        with_cin = db.query(Beneficiaire).filter(Beneficiaire.ben_typid == 0).count()
        with_passport = db.query(Beneficiaire).filter(Beneficiaire.ben_typid == 1).count()
        with_residence_card = db.query(Beneficiaire).filter(Beneficiaire.ben_typid == 2).count()
        
        return {
            "total": total_count,
            "male": male_count,
            "female": female_count,
            "type_1": type_1_count,
            "type_2": type_2_count,
            "with_email": with_email,
            "with_phone": with_phone,
            "with_address": with_address,
            "with_cin": with_cin,
            "with_passport": with_passport,
            "with_residence_card": with_residence_card,
            "male_percentage": round((male_count / total_count * 100) if total_count > 0 else 0, 2),
            "female_percentage": round((female_count / total_count * 100) if total_count > 0 else 0, 2),
            "email_percentage": round((with_email / total_count * 100) if total_count > 0 else 0, 2)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching statistics: {str(e)}")
