"""
CNSS API endpoints - Realistic Tunisian Social Security system simulation
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta
import random
import string

from app.core.database import get_db
from app.api.deps import get_current_user
from app.schemas.cnss import (
    CitizenResponse, AssuranceInfoResponse, BenefitsResponse,
    PaymentHistoryResponse, EmployerResponse, ContributionPaymentResponse,
    BenefitResponse, CNSSServiceResponse
)
from app.schemas.user import User

router = APIRouter()

# Mock data for realistic responses
MOCK_CITIZENS = {
    "12345678": {
        "id": 1,
        "cin": "12345678",
        "first_name": "<PERSON>",
        "last_name": "<PERSON>",
        "birth_date": "1985-03-15T00:00:00Z",
        "birth_place": "Tunis",
        "gender": "M",
        "address": "123 Avenue Habib Bourguiba, <PERSON><PERSON> 1000",
        "phone": "+216 71 123 456",
        "email": "<EMAIL>",
        "is_active": True,
        "created_at": "2020-01-15T10:30:00Z"
    },
    "87654321": {
        "id": 2,
        "cin": "87654321",
        "first_name": "Fatma",
        "last_name": "Trabelsi",
        "birth_date": "1990-07-22T00:00:00Z",
        "birth_place": "Sfax",
        "gender": "F",
        "address": "456 Rue de la République, Sfax 3000",
        "phone": "+216 74 987 654",
        "email": "<EMAIL>",
        "is_active": True,
        "created_at": "2021-03-10T14:20:00Z"
    }
}

MOCK_EMPLOYERS = {
    1: {
        "id": 1,
        "company_name": "TechCorp SARL",
        "registration_number": "RC123456789",
        "tax_id": "TAX987654321",
        "cnss_number": "CNSS001234567",
        "legal_form": "SARL",
        "activity_sector": "Technologies de l'information",
        "employee_count": 25,
        "email": "<EMAIL>",
        "phone": "+216 71 123 456",
        "address_line1": "123 Avenue Habib Bourguiba",
        "city": "Tunis",
        "postal_code": "1000",
        "region": "Tunis",
        "legal_rep_name": "Ahmed Ben Ali",
        "legal_rep_title": "Directeur Général",
        "legal_rep_cin": "12345678",
        "is_active": True,
        "is_verified": True,
        "registration_date": "2020-01-15T10:30:00Z",
        "last_declaration_date": "2024-01-15T10:30:00Z",
        "created_at": "2020-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
    }
}

def generate_payment_reference():
    """Generate a realistic payment reference"""
    return f"PAY{datetime.now().year}{random.randint(100000, 999999)}"

@router.post("/login")
async def cnss_login(credentials: dict):
    """
    CNSS Agent Authentication
    Simulates government employee login system
    """
    email = credentials.get("email")
    password = credentials.get("password")

    # Mock authentication - in real system would check against government database
    if email == "<EMAIL>" and password == "admin123":
        return {
            "access_token": "mock_jwt_token_" + ''.join(random.choices(string.ascii_letters + string.digits, k=32)),
            "token_type": "bearer",
            "user": {
                "id": 1,
                "email": "<EMAIL>",
                "first_name": "Admin",
                "last_name": "CNSS",
                "role": "admin",
                "department": "Direction Générale",
                "employee_id": "EMP001"
            }
        }

    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Identifiants invalides"
    )

@router.get("/employers", response_model=List[EmployerResponse])
async def get_employers(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all registered employers
    Requires authentication - only CNSS agents can access
    """
    employers = list(MOCK_EMPLOYERS.values())

    if search:
        employers = [
            emp for emp in employers
            if search.lower() in emp["company_name"].lower() or
               search.lower() in emp["cnss_number"].lower()
        ]

    return employers[skip:skip + limit]

@router.get("/employers/{employer_id}", response_model=EmployerResponse)
async def get_employer_details(
    employer_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a specific employer
    """
    if employer_id not in MOCK_EMPLOYERS:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Employeur non trouvé"
        )

    return MOCK_EMPLOYERS[employer_id]

@router.post("/employers", response_model=EmployerResponse)
async def create_employer(
    employer_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Register a new employer (admin only)
    """
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Accès réservé aux administrateurs"
        )

    # In real system, would validate and save to database
    new_id = max(MOCK_EMPLOYERS.keys()) + 1 if MOCK_EMPLOYERS else 1
    new_employer = {
        "id": new_id,
        **employer_data,
        "is_active": True,
        "is_verified": False,
        "registration_date": datetime.now().isoformat(),
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }

    MOCK_EMPLOYERS[new_id] = new_employer
    return new_employer

@router.get("/assurance-info/{cin}", response_model=AssuranceInfoResponse)
async def get_assurance_info(
    cin: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get social security/assurance status by CIN
    Core CNSS service for checking citizen coverage
    """
    if len(cin) != 8 or not cin.isdigit():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="CIN doit contenir exactement 8 chiffres"
        )

    if cin not in MOCK_CITIZENS:
        return {
            "citizen": None,
            "assurance_record": None,
            "employer": None,
            "status": "not_found",
            "message": "Aucun dossier trouvé pour ce CIN"
        }

    citizen = MOCK_CITIZENS[cin]

    # Mock assurance record
    assurance_record = {
        "id": 1,
        "citizen_id": citizen["id"],
        "employer_id": 1,
        "cnss_number": f"CNSS{cin}",
        "status": "active",
        "start_date": "2020-01-15T10:30:00Z",
        "end_date": None,
        "monthly_salary": 1200.0,
        "contribution_rate": 0.0925,
        "last_contribution_date": "2024-01-15T10:30:00Z",
        "total_contributions": 4440.0,  # 37 months * 120 TND
        "created_at": "2020-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
    }

    employer = MOCK_EMPLOYERS.get(1)

    return {
        "citizen": citizen,
        "assurance_record": assurance_record,
        "employer": employer,
        "status": "active",
        "message": "Dossier d'assurance actif"
    }

@router.get("/benefits/{cin}", response_model=BenefitsResponse)
async def get_benefits(
    cin: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get CNSS benefits for a citizen
    Shows pensions, medical coverage, unemployment benefits, etc.
    """
    if len(cin) != 8 or not cin.isdigit():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="CIN doit contenir exactement 8 chiffres"
        )

    if cin not in MOCK_CITIZENS:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Citoyen non trouvé"
        )

    citizen = MOCK_CITIZENS[cin]

    # Mock benefits based on citizen profile
    benefits = []

    # Medical coverage (everyone gets this)
    benefits.append({
        "id": 1,
        "citizen_id": citizen["id"],
        "benefit_type": "medical_coverage",
        "benefit_code": "MED001",
        "description": "Couverture médicale CNSS",
        "amount": None,
        "currency": "TND",
        "frequency": "continuous",
        "start_date": "2020-01-15T10:30:00Z",
        "end_date": None,
        "status": "active",
        "eligibility_criteria": "Cotisant actif ou ayant cotisé minimum 6 mois",
        "required_contributions_months": 6,
        "created_at": "2020-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
    })

    # Age-based pension eligibility
    birth_year = int(citizen["birth_date"][:4])
    current_year = datetime.now().year
    age = current_year - birth_year

    if age >= 60:  # Retirement age in Tunisia
        benefits.append({
            "id": 2,
            "citizen_id": citizen["id"],
            "benefit_type": "retirement_pension",
            "benefit_code": "PEN001",
            "description": "Pension de retraite",
            "amount": 850.0,
            "currency": "TND",
            "frequency": "monthly",
            "start_date": f"{birth_year + 60}-01-01T00:00:00Z",
            "end_date": None,
            "status": "active",
            "eligibility_criteria": "Âge minimum 60 ans et 120 mois de cotisations",
            "required_contributions_months": 120,
            "created_at": "2020-01-15T10:30:00Z",
            "updated_at": "2024-01-15T10:30:00Z"
        })

    active_benefits = len([b for b in benefits if b["status"] == "active"])

    return {
        "citizen": citizen,
        "benefits": benefits,
        "total_benefits": len(benefits),
        "active_benefits": active_benefits
    }

@router.get("/payments/{employer_id}", response_model=PaymentHistoryResponse)
async def get_payment_history(
    employer_id: int,
    year: Optional[int] = None,
    status: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get payment history for an employer
    Shows contribution payments, penalties, etc.
    """
    if employer_id not in MOCK_EMPLOYERS:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Employeur non trouvé"
        )

    employer = MOCK_EMPLOYERS[employer_id]

    # Generate mock payment history
    payments = []
    current_year = year or datetime.now().year

    for month in range(1, 13):
        payment_date = datetime(current_year, month, 15)
        due_date = datetime(current_year, month, 25)

        # Simulate some late payments
        is_late = random.choice([True, False, False, False])  # 25% chance of being late
        actual_payment_date = payment_date + timedelta(days=random.randint(0, 10)) if is_late else payment_date

        payment_status = "paid" if actual_payment_date <= datetime.now() else "pending"
        if is_late and payment_status == "paid":
            payment_status = "overdue" if actual_payment_date > due_date else "paid"

        base_salary = 30000.0  # Total monthly salaries for all employees
        contribution_amount = base_salary * 0.0925  # 9.25% contribution rate
        penalty_amount = contribution_amount * 0.05 if is_late else 0.0  # 5% penalty for late payment

        payments.append({
            "id": month,
            "employer_id": employer_id,
            "assurance_record_id": None,
            "payment_reference": generate_payment_reference(),
            "payment_type": "monthly",
            "period_month": month,
            "period_year": current_year,
            "base_salary": base_salary,
            "contribution_amount": contribution_amount,
            "penalty_amount": penalty_amount,
            "total_amount": contribution_amount + penalty_amount,
            "payment_date": actual_payment_date.isoformat() if payment_status == "paid" else None,
            "due_date": due_date.isoformat(),
            "status": payment_status,
            "payment_method": "bank_transfer" if payment_status == "paid" else None,
            "bank_reference": f"BNK{random.randint(100000, 999999)}" if payment_status == "paid" else None,
            "created_at": payment_date.isoformat(),
            "updated_at": actual_payment_date.isoformat()
        })

    # Filter by status if provided
    if status:
        payments = [p for p in payments if p["status"] == status]

    total_amount = sum(p["total_amount"] for p in payments)
    pending_amount = sum(p["total_amount"] for p in payments if p["status"] == "pending")
    overdue_count = len([p for p in payments if p["status"] == "overdue"])

    return {
        "employer": employer,
        "payments": payments,
        "total_payments": len(payments),
        "total_amount": total_amount,
        "pending_amount": pending_amount,
        "overdue_count": overdue_count
    }

# Dashboard endpoints (with and without prefix)
@router.get("/stats")
@router.get("/dashboard/stats")
async def get_dashboard_stats():
    """Get dashboard statistics"""
    return {
        "employers": {"total": 2, "change": "+12%"},
        "services": {"total": 3, "change": "+5%"},
        "apiExchanges": {"total": 156, "change": "+23%"},
        "users": {"total": 2, "change": "+2%"}
    }

@router.get("/recent-activity")
@router.get("/dashboard/recent-activity")
async def get_recent_activity():
    """Get recent system activity"""
    return [
        {
            "id": 1,
            "message": "Nouvelle déclaration mensuelle soumise par TechCorp SARL",
            "timestamp": "2024-01-25T10:30:00Z",
            "status": "success"
        },
        {
            "id": 2,
            "message": "Paiement de cotisations effectué par InnovateTech SA",
            "timestamp": "2024-01-25T09:15:00Z",
            "status": "success"
        }
    ]

@router.get("/system-health")
@router.get("/dashboard/system-health")
async def get_system_health():
    """Get system health status"""
    return {
        "database": {"status": "healthy", "responseTime": "12ms"},
        "apiGateway": {"status": "healthy", "responseTime": "45ms"},
        "externalApis": {"status": "warning", "responseTime": "234ms"},
        "storage": {"status": "healthy", "usage": "67%"}
    }

# Services endpoints
@router.get("/services")
async def get_services():
    """Get available CNSS services"""
    return [
        {
            "id": 1,
            "name": "Déclaration Mensuelle",
            "code": "DECL_MONTHLY",
            "description": "Service de déclaration mensuelle des salaires et cotisations",
            "service_type": "declaration",
            "status": "active",
            "endpoint_url": "/api/declarations/monthly",
            "http_method": "POST",
            "requires_auth": True,
            "version": "1.0",
            "created_at": "2024-01-01T00:00:00Z"
        },
        {
            "id": 2,
            "name": "Paiement Cotisations",
            "code": "PAY_CONTRIB",
            "description": "Service de paiement des cotisations sociales",
            "service_type": "payment",
            "status": "active",
            "endpoint_url": "/api/payments/contributions",
            "http_method": "POST",
            "requires_auth": True,
            "version": "1.0",
            "created_at": "2024-01-01T00:00:00Z"
        },
        {
            "id": 3,
            "name": "Consultation Dossier",
            "code": "INQ_DOSSIER",
            "description": "Service de consultation du dossier employeur",
            "service_type": "inquiry",
            "status": "active",
            "endpoint_url": "/api/inquiries/dossier",
            "http_method": "GET",
            "requires_auth": True,
            "version": "1.0",
            "created_at": "2024-01-01T00:00:00Z"
        }
    ]

# Additional endpoints that frontend is trying to access
@router.delete("/employers/{employer_id}")
async def delete_employer(employer_id: int):
    """Delete an employer"""
    return {"message": f"Employer {employer_id} deleted successfully"}

@router.get("/services/health")
async def get_services_health():
    """Get services health status"""
    return {
        "status": "healthy",
        "services": [
            {"name": "Declaration Service", "status": "up", "response_time": "45ms"},
            {"name": "Payment Service", "status": "up", "response_time": "32ms"},
            {"name": "History Service", "status": "up", "response_time": "28ms"}
        ]
    }