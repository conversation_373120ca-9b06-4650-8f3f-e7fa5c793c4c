"""
Employeurs API endpoints
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_

from app.core.database import get_db
from app.models.employeur import Employeur
from app.api.deps import get_current_user

router = APIRouter()

@router.get("/", response_model=List[dict])
async def get_employeurs(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search in company name, email, or activity"),
    db: Session = Depends(get_db)
    # current_user = Depends(get_current_user)  # Temporarily disabled for testing
):
    """
    Get list of employers with pagination and search
    """
    try:
        query = db.query(Employeur)
        
        # Apply search filter if provided
        if search:
            search_filter = or_(
                Employeur.emp_rais.ilike(f"%{search}%"),
                Employeur.emp_rais_ar.ilike(f"%{search}%"),
                Employeur.emp_email.ilike(f"%{search}%"),
                Employeur.emp_activite.ilike(f"%{search}%"),
                Employeur.emp_sigle.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)
        
        # Apply pagination
        employeurs = query.offset(skip).limit(limit).all()
        
        # Convert to dict for JSON response
        result = [emp.to_dict() for emp in employeurs]
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching employers: {str(e)}")

@router.get("/count")
async def get_employeurs_count(
    search: Optional[str] = Query(None, description="Search filter"),
    db: Session = Depends(get_db)
    # current_user = Depends(get_current_user)  # Temporarily disabled for testing
):
    """
    Get total count of employers
    """
    try:
        query = db.query(Employeur)
        
        # Apply search filter if provided
        if search:
            search_filter = or_(
                Employeur.emp_rais.ilike(f"%{search}%"),
                Employeur.emp_rais_ar.ilike(f"%{search}%"),
                Employeur.emp_email.ilike(f"%{search}%"),
                Employeur.emp_activite.ilike(f"%{search}%"),
                Employeur.emp_sigle.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)
        
        count = query.count()
        return {"count": count}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error counting employers: {str(e)}")

@router.get("/{emp_mat}/{emp_cle}")
async def get_employeur(
    emp_mat: int,
    emp_cle: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get specific employer by matricule and key
    """
    try:
        employeur = db.query(Employeur).filter(
            and_(
                Employeur.emp_mat == emp_mat,
                Employeur.emp_cle == emp_cle
            )
        ).first()
        
        if not employeur:
            raise HTTPException(status_code=404, detail="Employer not found")
        
        return employeur.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching employer: {str(e)}")

@router.get("/statistics")
async def get_employeurs_statistics(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get employers statistics
    """
    try:
        total_count = db.query(Employeur).count()
        active_count = db.query(Employeur).filter(Employeur.emp_dtdact.is_(None)).count()
        inactive_count = total_count - active_count
        
        # Count by activity type (if available)
        with_activity = db.query(Employeur).filter(Employeur.emp_activite.isnot(None)).count()
        
        # Count with email
        with_email = db.query(Employeur).filter(Employeur.emp_email.isnot(None)).count()
        
        return {
            "total": total_count,
            "active": active_count,
            "inactive": inactive_count,
            "with_activity": with_activity,
            "with_email": with_email,
            "email_percentage": round((with_email / total_count * 100) if total_count > 0 else 0, 2)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching statistics: {str(e)}")
