"""
Assure (Insured Person) model for Oracle database
"""
from sqlalchemy import Column, Integer, String, Date, Numeric
from app.core.database import Base

class Assure(Base):
    """
    Assure table model matching Oracle database structure
    """
    __tablename__ = "assure"
    
    # Primary key fields
    ass_mat = Column(Integer, primary_key=True, nullable=False)
    ass_cle = Column(Integer, primary_key=True, nullable=False)
    
    # Administrative fields
    bur_cod = Column(Integer, nullable=True)
    pay_cod = Column(Integer, nullable=False)
    
    # Employer reference
    emp_mat = Column(Integer, nullable=True)
    emp_cle = Column(Integer, nullable=True)
    
    # Identification and status
    ass_iu = Column(Integer, nullable=False, comment="1: racine pens existe dans suivi_chargement_pensionne non RG, 2: racine pens existe dans suivi_chargement_pensionne non RG inexistant cvfstag")
    
    # Date fields
    ass_dteff = Column(Date, nullable=True)  # Date d'effet
    ass_dtimmat = Column(Date, nullable=True)  # Date d'immatriculation
    ass_dtsim = Column(Date, nullable=True)  # Date de simulation
    ass_dtvalid = Column(Date, nullable=True)  # Date de validation
    ass_dttcent = Column(Date, nullable=True)  # Date de traitement central
    ass_dtassuj = Column(Date, nullable=True)  # Date d'assujettissement
    
    # Administrative codes
    ass_codag = Column(Integer, nullable=True)
    
    # Financial and administrative fields
    ass_saluni = Column(Integer, nullable=True)  # Salaire unique
    ass_rib = Column(Integer, nullable=True)  # RIB
    ass_cnrps = Column(Integer, nullable=True)  # CNRPS number
    ass_brcreat = Column(Integer, nullable=True)  # Bureau de création
    
    # Status flags
    ass_flpr = Column(Integer, nullable=True)  # Flag PR
    ass_derreg = Column(Integer, nullable=True)  # Dernier régime
    
    # Agent and processing fields
    ass_agent = Column(Integer, nullable=True)
    etat_chargement = Column(String(100), nullable=True)
    ben_rc = Column(Integer, nullable=True)
    methrecepdossid = Column(Integer, nullable=True)
    
    def __repr__(self):
        return f"<Assure(mat={self.ass_mat}, cle={self.ass_cle})>"
    
    def to_dict(self):
        """Convert model to dictionary for JSON serialization"""
        return {
            'ass_mat': self.ass_mat,
            'ass_cle': self.ass_cle,
            'bur_cod': self.bur_cod,
            'pay_cod': self.pay_cod,
            'emp_mat': self.emp_mat,
            'emp_cle': self.emp_cle,
            'ass_iu': self.ass_iu,
            'ass_dteff': self.ass_dteff.isoformat() if self.ass_dteff else None,
            'ass_dtimmat': self.ass_dtimmat.isoformat() if self.ass_dtimmat else None,
            'ass_dtsim': self.ass_dtsim.isoformat() if self.ass_dtsim else None,
            'ass_dtvalid': self.ass_dtvalid.isoformat() if self.ass_dtvalid else None,
            'ass_dttcent': self.ass_dttcent.isoformat() if self.ass_dttcent else None,
            'ass_dtassuj': self.ass_dtassuj.isoformat() if self.ass_dtassuj else None,
            'ass_codag': self.ass_codag,
            'ass_saluni': self.ass_saluni,
            'ass_rib': self.ass_rib,
            'ass_cnrps': self.ass_cnrps,
            'ass_brcreat': self.ass_brcreat,
            'ass_flpr': self.ass_flpr,
            'ass_derreg': self.ass_derreg,
            'ass_agent': self.ass_agent,
            'etat_chargement': self.etat_chargement,
            'ben_rc': self.ben_rc,
            'methrecepdossid': self.methrecepdossid
        }
