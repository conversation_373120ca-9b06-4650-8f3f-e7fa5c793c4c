"""
Assure (Insured Person) model for Oracle database - Simplified for your setup
"""
from sqlalchemy import Column, Integer, String, Date
from app.core.database import Base

class Assure(Base):
    """
    Assure table model matching your Oracle database structure
    """
    __tablename__ = "assure"

    # Primary key fields
    ass_mat = Column(Integer, primary_key=True, nullable=False)
    ass_cle = Column(Integer, primary_key=True, nullable=False)

    # Employer reference (Foreign Key) - EXACTLY matching your Oracle table
    emp_mat = Column(Integer, nullable=True)
    emp_cle = Column(Integer, nullable=True)

    # Date fields - EXACTLY matching your Oracle table
    ass_dteff = Column(Date, nullable=True)  # Date d'effet
    ass_dtimmat = Column(Date, nullable=True)  # Date d'immatriculation

    # Financial information - EXACTLY matching your Oracle table
    ass_rib = Column(String(24), nullable=True)  # RIB
    ass_cnrps = Column(String(20), nullable=True)  # CNRPS number

    # Status - EXACTLY matching your Oracle table
    etat_chargement = Column(String(20), nullable=True)
    
    def __repr__(self):
        return f"<Assure(mat={self.ass_mat}, cle={self.ass_cle})>"
    
    def to_dict(self):
        """Convert model to dictionary for JSON serialization"""
        return {
            'ass_mat': self.ass_mat,
            'ass_cle': self.ass_cle,
            'emp_mat': self.emp_mat,
            'emp_cle': self.emp_cle,
            'ass_dteff': self.ass_dteff.isoformat() if self.ass_dteff else None,
            'ass_dtimmat': self.ass_dtimmat.isoformat() if self.ass_dtimmat else None,
            'ass_rib': self.ass_rib,
            'ass_cnrps': self.ass_cnrps,
            'etat_chargement': self.etat_chargement
        }
