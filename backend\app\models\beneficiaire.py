"""
Beneficiaire (Beneficiary) model for Oracle database
"""
from sqlalchemy import Column, Integer, String, Date, CHAR
from app.core.database import Base

class Beneficiaire(Base):
    """
    Beneficiaire table model matching Oracle database structure
    """
    __tablename__ = "beneficiaire"
    
    # Primary key fields
    ben_iducnss = Column(Integer, primary_key=True, nullable=False)
    
    # Location fields
    loc_codpos = Column(Integer, nullable=True)
    loc_indice = Column(Integer, nullable=True)
    
    # Assure reference
    ass_mat = Column(Integer, nullable=False)
    ass_cle = Column(Integer, nullable=False)
    pay_cod = Column(Integer, nullable=False)
    
    # Beneficiary classification
    ben_type = Column(Integer, nullable=False)
    ben_rang = Column(Integer, nullable=False)
    ben_iu = Column(Integer, nullable=True)
    
    # Personal information - Names
    ben_nom = Column(String(30), nullable=True)  # Last name
    ben_nom_ar = Column(String(30), nullable=True)  # Last name in Arabic
    ben_prenom = Column(String(30), nullable=True)  # First name
    ben_prn_ar = Column(String(30), nullable=True)  # First name in Arabic
    ben_prnper = Column(String(30), nullable=True)  # Father's first name
    ben_prnper_ar = Column(String(30), nullable=True)  # Father's first name in Arabic
    ben_prngp = Column(String(30), nullable=True)  # Grandfather's first name
    ben_prngp_ar = Column(String(30), nullable=True)  # Grandfather's first name in Arabic
    ben_prnmer = Column(String(30), nullable=True)  # Mother's first name
    ben_prnmer_ar = Column(String(30), nullable=True)  # Mother's first name in Arabic
    ben_nommer = Column(String(30), nullable=True)  # Mother's last name
    ben_nommer_ar = Column(String(30), nullable=True)  # Mother's last name in Arabic
    
    # Personal details
    ben_sexe = Column(Integer, nullable=True)  # Gender
    ben_sitfam = Column(Integer, nullable=True)  # Family status
    ben_dtnais = Column(Date, nullable=True)  # Birth date
    ben_lnais = Column(String(30), nullable=True)  # Birth place
    ben_lnais_ar = Column(String(30), nullable=True)  # Birth place in Arabic
    ben_dtsitfam = Column(Date, nullable=True)  # Family status date
    
    # Identification documents
    ben_typid = Column(Integer, nullable=True, comment="0: CIN, 1: Passeport, 2: Carte sejour")
    ben_numid = Column(String(15), nullable=True)  # ID number
    ben_dtid = Column(Date, nullable=True)  # ID date
    
    # Address information
    ben_adr = Column(String(120), nullable=True)  # Address
    ben_adr_ar = Column(String(120), nullable=True)  # Address in Arabic
    ben_codpiece = Column(CHAR(1), nullable=True)
    
    # Contact information
    ben_tel = Column(Integer, nullable=True)  # Phone
    ben_email = Column(String(30), nullable=True)  # Email
    
    # Administrative fields
    ben_nactn = Column(Integer, nullable=True)
    ben_aactn = Column(Integer, nullable=True)
    ben_lactn = Column(String(20), nullable=True)
    
    # Status and dates
    ben_draf = Column(Integer, nullable=True)
    ben_dtdaf = Column(Date, nullable=True)
    ben_dtfaf = Column(Date, nullable=True)
    ben_dsoin = Column(Integer, nullable=True)
    ben_dtdsoin = Column(Date, nullable=True)
    ben_dtfsoin = Column(Date, nullable=True)
    
    # Flags and references
    ben_flper = Column(Integer, nullable=True)
    ben_lpar = Column(Integer, nullable=True)
    ben_agent = Column(Integer, nullable=True)
    etr_mat = Column(Integer, nullable=True)
    ben_imadat = Column(Integer, nullable=True)
    ben_consulat = Column(Integer, nullable=True)
    
    # Geographic codes
    ben_arrondis = Column(Integer, nullable=True)
    ben_annreg = Column(Integer, nullable=True)
    ben_numact = Column(Integer, nullable=True)
    ben_municip = Column(Integer, nullable=True)
    ben_validact = Column(Integer, nullable=True)
    arrondissement_gvt_cod = Column(Integer, nullable=True)
    arrondissement_dlg_cod = Column(Integer, nullable=True)
    arrondissement_mun_code = Column(Integer, nullable=True)
    arrondissement_aro_code = Column(Integer, nullable=True)
    
    # Standardized names
    pre_fra_std = Column(String(250), nullable=True)
    nom_fra_std = Column(String(250), nullable=True)
    
    # Additional fields
    ben_rc = Column(Integer, nullable=True)
    ben_identitemadania = Column(String(500), nullable=True)
    ben_identitemeremadania = Column(String(500), nullable=True)
    ben_dtsaisie = Column(Date, nullable=True)  # Entry date
    ben_dtmaj = Column(Date, nullable=True)  # Last update date
    
    def __repr__(self):
        return f"<Beneficiaire(id={self.ben_iducnss}, nom='{self.ben_nom}', prenom='{self.ben_prenom}')>"
    
    def to_dict(self):
        """Convert model to dictionary for JSON serialization"""
        return {
            'ben_iducnss': self.ben_iducnss,
            'loc_codpos': self.loc_codpos,
            'loc_indice': self.loc_indice,
            'ass_mat': self.ass_mat,
            'ass_cle': self.ass_cle,
            'pay_cod': self.pay_cod,
            'ben_type': self.ben_type,
            'ben_rang': self.ben_rang,
            'ben_iu': self.ben_iu,
            'ben_nom': self.ben_nom,
            'ben_nom_ar': self.ben_nom_ar,
            'ben_prenom': self.ben_prenom,
            'ben_prn_ar': self.ben_prn_ar,
            'ben_prnper': self.ben_prnper,
            'ben_prnper_ar': self.ben_prnper_ar,
            'ben_prngp': self.ben_prngp,
            'ben_prngp_ar': self.ben_prngp_ar,
            'ben_prnmer': self.ben_prnmer,
            'ben_prnmer_ar': self.ben_prnmer_ar,
            'ben_nommer': self.ben_nommer,
            'ben_nommer_ar': self.ben_nommer_ar,
            'ben_sexe': self.ben_sexe,
            'ben_sitfam': self.ben_sitfam,
            'ben_dtnais': self.ben_dtnais.isoformat() if self.ben_dtnais else None,
            'ben_lnais': self.ben_lnais,
            'ben_lnais_ar': self.ben_lnais_ar,
            'ben_dtsitfam': self.ben_dtsitfam.isoformat() if self.ben_dtsitfam else None,
            'ben_typid': self.ben_typid,
            'ben_numid': self.ben_numid,
            'ben_dtid': self.ben_dtid.isoformat() if self.ben_dtid else None,
            'ben_adr': self.ben_adr,
            'ben_adr_ar': self.ben_adr_ar,
            'ben_codpiece': self.ben_codpiece,
            'ben_tel': self.ben_tel,
            'ben_email': self.ben_email,
            'ben_nactn': self.ben_nactn,
            'ben_aactn': self.ben_aactn,
            'ben_lactn': self.ben_lactn,
            'ben_draf': self.ben_draf,
            'ben_dtdaf': self.ben_dtdaf.isoformat() if self.ben_dtdaf else None,
            'ben_dtfaf': self.ben_dtfaf.isoformat() if self.ben_dtfaf else None,
            'ben_dsoin': self.ben_dsoin,
            'ben_dtdsoin': self.ben_dtdsoin.isoformat() if self.ben_dtdsoin else None,
            'ben_dtfsoin': self.ben_dtfsoin.isoformat() if self.ben_dtfsoin else None,
            'ben_flper': self.ben_flper,
            'ben_lpar': self.ben_lpar,
            'ben_agent': self.ben_agent,
            'etr_mat': self.etr_mat,
            'ben_imadat': self.ben_imadat,
            'ben_consulat': self.ben_consulat,
            'ben_arrondis': self.ben_arrondis,
            'ben_annreg': self.ben_annreg,
            'ben_numact': self.ben_numact,
            'ben_municip': self.ben_municip,
            'ben_validact': self.ben_validact,
            'arrondissement_gvt_cod': self.arrondissement_gvt_cod,
            'arrondissement_dlg_cod': self.arrondissement_dlg_cod,
            'arrondissement_mun_code': self.arrondissement_mun_code,
            'arrondissement_aro_code': self.arrondissement_aro_code,
            'pre_fra_std': self.pre_fra_std,
            'nom_fra_std': self.nom_fra_std,
            'ben_rc': self.ben_rc,
            'ben_identitemadania': self.ben_identitemadania,
            'ben_identitemeremadania': self.ben_identitemeremadania,
            'ben_dtsaisie': self.ben_dtsaisie.isoformat() if self.ben_dtsaisie else None,
            'ben_dtmaj': self.ben_dtmaj.isoformat() if self.ben_dtmaj else None
        }
