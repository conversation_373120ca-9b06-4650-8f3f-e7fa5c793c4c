"""
Beneficiaire (Beneficiary) model for Oracle database - Matching your actual table structure
"""
from sqlalchemy import Column, Integer, String, Date
from app.core.database import Base

class Beneficiaire(Base):
    """
    Beneficiaire table model matching your actual Oracle database structure
    """
    __tablename__ = "beneficiaire"

    # Primary key fields
    ben_mat = Column(Integer, primary_key=True, nullable=False)
    ben_cle = Column(Integer, primary_key=True, nullable=False)

    # Unique identifier
    ben_iducnss = Column(String(20), nullable=True)

    # Personal information - Names
    ben_nom = Column(String(50), nullable=True)  # Last name
    ben_prenom = Column(String(50), nullable=True)  # First name
    ben_nom_ar = Column(String(50), nullable=True)  # Last name in Arabic
    ben_prn_ar = Column(String(50), nullable=True)  # First name in Arabic

    # Contact information
    ben_email = Column(String(100), nullable=True)  # Email
    ben_tel = Column(String(20), nullable=True)  # Phone

    # Identification documents
    ben_numid = Column(String(20), nullable=True)  # ID number
    ben_typid = Column(Integer, nullable=True)  # ID type

    # Personal details
    ben_dtnais = Column(Date, nullable=True)  # Birth date
    ben_dtdeces = Column(Date, nullable=True)  # Death date
    ben_sexe = Column(Integer, nullable=True)  # Gender

    # Beneficiary classification
    ben_type = Column(Integer, nullable=True)  # Type of beneficiary

    # Status
    ben_statut = Column(String(20), nullable=True)  # Status

    # Audit fields
    created_at = Column(Date, nullable=True)
    updated_at = Column(Date, nullable=True)
    
    def __repr__(self):
        return f"<Beneficiaire(mat={self.ben_mat}, cle={self.ben_cle}, nom='{self.ben_nom}', prenom='{self.ben_prenom}')>"
    
    def to_dict(self):
        """Convert model to dictionary for JSON serialization"""
        return {
            'ben_mat': self.ben_mat,
            'ben_cle': self.ben_cle,
            'ben_iducnss': self.ben_iducnss,
            'ben_nom': self.ben_nom,
            'ben_prenom': self.ben_prenom,
            'ben_nom_ar': self.ben_nom_ar,
            'ben_prn_ar': self.ben_prn_ar,
            'ben_email': self.ben_email,
            'ben_tel': self.ben_tel,
            'ben_numid': self.ben_numid,
            'ben_typid': self.ben_typid,
            'ben_dtnais': self.ben_dtnais.isoformat() if self.ben_dtnais else None,
            'ben_dtdeces': self.ben_dtdeces.isoformat() if self.ben_dtdeces else None,
            'ben_sexe': self.ben_sexe,
            'ben_type': self.ben_type,
            'ben_statut': self.ben_statut,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
