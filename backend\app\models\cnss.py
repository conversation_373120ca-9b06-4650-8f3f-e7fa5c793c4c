"""
CNSS-specific models for the Tunisian Social Security system
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Float, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

class Citizen(Base):
    """Model for Tunisian citizens with CIN"""
    __tablename__ = "citizens"
    
    id = Column(Integer, primary_key=True, index=True)
    cin = Column(String(8), unique=True, index=True, nullable=False)  # Tunisian CIN format
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    birth_date = Column(DateTime, nullable=False)
    birth_place = Column(String(200))
    gender = Column(String(1))  # M/F
    address = Column(Text)
    phone = Column(String(20))
    email = Column(String(255))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    assurance_records = relationship("AssuranceRecord", back_populates="citizen")
    benefits = relationship("Benefit", back_populates="citizen")

class AssuranceRecord(Base):
    """Social Security/Assurance records for citizens"""
    __tablename__ = "assurance_records"
    
    id = Column(Integer, primary_key=True, index=True)
    citizen_id = Column(Integer, ForeignKey("citizens.id"), nullable=False)
    cnss_number = Column(String(15), unique=True, index=True)  # CNSS registration number
    employer_id = Column(Integer, ForeignKey("employers.id"))
    status = Column(String(20), default="active")  # active, inactive, suspended
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime)
    monthly_salary = Column(Float)
    contribution_rate = Column(Float, default=0.0925)  # 9.25% standard rate
    last_contribution_date = Column(DateTime)
    total_contributions = Column(Float, default=0.0)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    citizen = relationship("Citizen", back_populates="assurance_records")
    employer = relationship("Employer", back_populates="assurance_records")
    payments = relationship("ContributionPayment", back_populates="assurance_record")

class Employer(Base):
    """Enhanced employer model for CNSS system"""
    __tablename__ = "employers"
    
    id = Column(Integer, primary_key=True, index=True)
    company_name = Column(String(255), nullable=False)
    registration_number = Column(String(50), unique=True, index=True)
    tax_id = Column(String(50), unique=True, index=True)
    cnss_number = Column(String(20), unique=True, index=True)
    legal_form = Column(String(50))  # SARL, SA, etc.
    activity_sector = Column(String(100))
    employee_count = Column(Integer, default=0)
    
    # Contact information
    email = Column(String(255))
    phone = Column(String(20))
    fax = Column(String(20))
    
    # Address
    address_line1 = Column(String(255))
    address_line2 = Column(String(255))
    city = Column(String(100))
    postal_code = Column(String(10))
    region = Column(String(100))
    
    # Legal representative
    legal_rep_name = Column(String(200))
    legal_rep_title = Column(String(100))
    legal_rep_cin = Column(String(8))
    
    # Status
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    registration_date = Column(DateTime, default=datetime.utcnow)
    last_declaration_date = Column(DateTime)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    assurance_records = relationship("AssuranceRecord", back_populates="employer")
    payments = relationship("ContributionPayment", back_populates="employer")

class ContributionPayment(Base):
    """Payment records for CNSS contributions"""
    __tablename__ = "contribution_payments"
    
    id = Column(Integer, primary_key=True, index=True)
    employer_id = Column(Integer, ForeignKey("employers.id"), nullable=False)
    assurance_record_id = Column(Integer, ForeignKey("assurance_records.id"))
    
    payment_reference = Column(String(50), unique=True, index=True)
    payment_type = Column(String(30))  # monthly, quarterly, annual
    period_month = Column(Integer)  # 1-12
    period_year = Column(Integer)
    
    base_salary = Column(Float, nullable=False)
    contribution_amount = Column(Float, nullable=False)
    penalty_amount = Column(Float, default=0.0)
    total_amount = Column(Float, nullable=False)
    
    payment_date = Column(DateTime)
    due_date = Column(DateTime, nullable=False)
    status = Column(String(20), default="pending")  # pending, paid, overdue, cancelled
    
    payment_method = Column(String(30))  # bank_transfer, check, cash
    bank_reference = Column(String(100))
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    employer = relationship("Employer", back_populates="payments")
    assurance_record = relationship("AssuranceRecord", back_populates="payments")

class Benefit(Base):
    """CNSS benefits for citizens"""
    __tablename__ = "benefits"
    
    id = Column(Integer, primary_key=True, index=True)
    citizen_id = Column(Integer, ForeignKey("citizens.id"), nullable=False)
    
    benefit_type = Column(String(50), nullable=False)  # pension, medical, unemployment, etc.
    benefit_code = Column(String(20))
    description = Column(Text)
    
    amount = Column(Float)
    currency = Column(String(3), default="TND")
    frequency = Column(String(20))  # monthly, quarterly, annual, one-time
    
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime)
    status = Column(String(20), default="active")  # active, suspended, terminated
    
    eligibility_criteria = Column(Text)
    required_contributions_months = Column(Integer)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    citizen = relationship("Citizen", back_populates="benefits")

class CNSSService(Base):
    """Available CNSS services"""
    __tablename__ = "cnss_services"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(200), nullable=False)
    code = Column(String(50), unique=True, index=True)
    description = Column(Text)
    category = Column(String(50))  # declaration, payment, inquiry, certificate
    
    endpoint_url = Column(String(255))
    http_method = Column(String(10), default="GET")
    requires_auth = Column(Boolean, default=True)
    
    target_audience = Column(String(50))  # employer, citizen, agent
    processing_time = Column(String(100))  # "Immediate", "1-3 days", etc.
    required_documents = Column(Text)
    
    is_active = Column(Boolean, default=True)
    version = Column(String(10), default="1.0")
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
