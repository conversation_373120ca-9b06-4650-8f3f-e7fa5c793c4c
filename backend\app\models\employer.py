"""
Employer model for CNSS platform
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text
from sqlalchemy.sql import func
from app.core.database import Base


class Employer(Base):
    """Employer model for managing company information"""
    
    __tablename__ = "employers"
    
    id = Column(Integer, primary_key=True, index=True)
    company_name = Column(String(255), nullable=False, index=True)
    registration_number = Column(String(50), unique=True, nullable=False, index=True)
    tax_id = Column(String(50), unique=True, nullable=False)
    cnss_number = Column(String(50), unique=True, nullable=False, index=True)
    
    # Contact Information
    email = Column(String(255), nullable=False)
    phone = Column(String(20), nullable=True)
    fax = Column(String(20), nullable=True)
    website = Column(String(255), nullable=True)
    
    # Address Information
    address_line1 = Column(String(255), nullable=False)
    address_line2 = Column(String(255), nullable=True)
    city = Column(String(100), nullable=False)
    postal_code = Column(String(20), nullable=False)
    region = Column(String(100), nullable=False)
    country = Column(String(100), default="Tunisia", nullable=False)
    
    # Business Information
    industry_sector = Column(String(100), nullable=True)
    business_type = Column(String(100), nullable=True)
    employee_count = Column(Integer, nullable=True)
    annual_revenue = Column(String(50), nullable=True)
    
    # Legal Representative
    legal_rep_name = Column(String(255), nullable=False)
    legal_rep_title = Column(String(100), nullable=False)
    legal_rep_email = Column(String(255), nullable=True)
    legal_rep_phone = Column(String(20), nullable=True)
    
    # Status and Metadata
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    notes = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<Employer(id={self.id}, name='{self.company_name}', cnss='{self.cnss_number}')>"
    
    @property
    def full_address(self) -> str:
        """Get formatted full address"""
        address_parts = [self.address_line1]
        if self.address_line2:
            address_parts.append(self.address_line2)
        address_parts.extend([self.city, self.postal_code, self.region, self.country])
        return ", ".join(address_parts)
