"""
Employeur (Employer) model for Oracle database
"""
from sqlalchemy import Column, Integer, String, Date, Numeric, CHAR
from app.core.database import Base

class Employeur(Base):
    """
    Employeur table model matching Oracle database structure
    """
    __tablename__ = "employeur"
    
    # Primary key fields
    emp_mat = Column(Integer, primary_key=True, nullable=False)
    emp_cle = Column(Integer, primary_key=True, nullable=False)
    
    # Administrative fields
    adm_cod = Column(String(4), nullable=True)
    pat_num = Column(Integer, nullable=True)
    pat_cle = Column(CHAR(1), nullable=True)
    atv_cod = Column(Integer, nullable=True)
    rpl_id = Column(String(15), nullable=True)
    
    # Location fields
    loc_codpos = Column(Integer, nullable=True)
    loc_indice = Column(Integer, nullable=True)
    bur_cod = Column(Integer, nullable=True)
    atm_ref = Column(Integer, nullable=True)
    dct_dtcot = Column(Integer, nullable=True)
    loc_loc_codpos = Column(Integer, nullable=False)
    loc_loc_indice = Column(Integer, nullable=False)
    reg_cod = Column(Integer, nullable=False)
    pay_cod = Column(Integer, nullable=False)
    
    # Date fields
    emp_dteff = Column(Date, nullable=True)
    emp_dtassuj = Column(Date, nullable=True)
    emp_dtdact = Column(Date, nullable=True)
    emp_dtregcp = Column(Date, nullable=True)
    emp_dtaff = Column(Date, nullable=True)
    emp_dtmaj = Column(Date, nullable=True)
    emp_dtfinregcp = Column(Date, nullable=True)
    
    # Company information
    emp_rais = Column(String(80), nullable=True)  # Company name
    emp_rais_ar = Column(String(80), nullable=True)  # Company name in Arabic
    emp_tel = Column(String(10), nullable=True)
    emp_fax = Column(String(10), nullable=True)
    emp_email = Column(String(60), nullable=True)
    emp_sigle = Column(String(15), nullable=True)
    emp_enseig = Column(String(10), nullable=True)
    emp_activite = Column(String(60), nullable=True)
    
    # Registration and legal fields
    emp_nregc = Column(String(12), nullable=True)
    emp_lregc = Column(String(30), nullable=True)  # Tribunal
    emp_cregcmp = Column(CHAR(1), nullable=True)
    
    # Financial fields
    emp_estint = Column(Numeric(15, 3), nullable=True)
    emp_rib = Column(Integer, nullable=True)
    emp_capres = Column(Integer, nullable=True)
    
    # Classification fields
    emp_catprof = Column(String(1), nullable=True)
    emp_typaffi = Column(Integer, nullable=True)
    emp_numadh = Column(Integer, nullable=True)
    
    # Address fields
    emp_adrcor = Column(String(100), nullable=True)
    emp_adrcor_ar = Column(String(100), nullable=True)
    emp_deradr = Column(String(100), nullable=True)
    emp_derar_ar = Column(String(100), nullable=True)
    
    # Reference fields
    emp_exaffmat = Column(Integer, nullable=True)
    emp_exaffcle = Column(Integer, nullable=True)
    emp_matag = Column(Integer, nullable=True)
    
    # Status flags
    emp_inconnu = Column(Integer, nullable=True)
    emp_flagper = Column(Integer, nullable=True)
    
    # Representative information for complementary retirement regime
    emp_identrepadhregc = Column(String(100), nullable=True)
    emp_qualrepadhregc = Column(String(100), nullable=True)
    
    def __repr__(self):
        return f"<Employeur(mat={self.emp_mat}, cle={self.emp_cle}, rais='{self.emp_rais}')>"
    
    def to_dict(self):
        """Convert model to dictionary for JSON serialization"""
        return {
            'emp_mat': self.emp_mat,
            'emp_cle': self.emp_cle,
            'adm_cod': self.adm_cod,
            'pat_num': self.pat_num,
            'pat_cle': self.pat_cle,
            'atv_cod': self.atv_cod,
            'rpl_id': self.rpl_id,
            'loc_codpos': self.loc_codpos,
            'loc_indice': self.loc_indice,
            'bur_cod': self.bur_cod,
            'atm_ref': self.atm_ref,
            'dct_dtcot': self.dct_dtcot,
            'loc_loc_codpos': self.loc_loc_codpos,
            'loc_loc_indice': self.loc_loc_indice,
            'reg_cod': self.reg_cod,
            'pay_cod': self.pay_cod,
            'emp_dteff': self.emp_dteff.isoformat() if self.emp_dteff else None,
            'emp_dtassuj': self.emp_dtassuj.isoformat() if self.emp_dtassuj else None,
            'emp_dtdact': self.emp_dtdact.isoformat() if self.emp_dtdact else None,
            'emp_dtregcp': self.emp_dtregcp.isoformat() if self.emp_dtregcp else None,
            'emp_dtaff': self.emp_dtaff.isoformat() if self.emp_dtaff else None,
            'emp_dtmaj': self.emp_dtmaj.isoformat() if self.emp_dtmaj else None,
            'emp_dtfinregcp': self.emp_dtfinregcp.isoformat() if self.emp_dtfinregcp else None,
            'emp_rais': self.emp_rais,
            'emp_rais_ar': self.emp_rais_ar,
            'emp_tel': self.emp_tel,
            'emp_fax': self.emp_fax,
            'emp_email': self.emp_email,
            'emp_sigle': self.emp_sigle,
            'emp_enseig': self.emp_enseig,
            'emp_activite': self.emp_activite,
            'emp_nregc': self.emp_nregc,
            'emp_lregc': self.emp_lregc,
            'emp_cregcmp': self.emp_cregcmp,
            'emp_estint': float(self.emp_estint) if self.emp_estint else None,
            'emp_rib': self.emp_rib,
            'emp_capres': self.emp_capres,
            'emp_catprof': self.emp_catprof,
            'emp_typaffi': self.emp_typaffi,
            'emp_numadh': self.emp_numadh,
            'emp_adrcor': self.emp_adrcor,
            'emp_adrcor_ar': self.emp_adrcor_ar,
            'emp_deradr': self.emp_deradr,
            'emp_derar_ar': self.emp_derar_ar,
            'emp_exaffmat': self.emp_exaffmat,
            'emp_exaffcle': self.emp_exaffcle,
            'emp_matag': self.emp_matag,
            'emp_inconnu': self.emp_inconnu,
            'emp_flagper': self.emp_flagper,
            'emp_identrepadhregc': self.emp_identrepadhregc,
            'emp_qualrepadhregc': self.emp_qualrepadhregc
        }
