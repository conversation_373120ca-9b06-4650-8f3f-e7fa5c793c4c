"""
Employeur (Employer) model for Oracle database - Simplified for your setup
"""
from sqlalchemy import Column, Integer, String, Date
from app.core.database import Base

class Employeur(Base):
    """
    Employeur table model matching your Oracle database structure
    """
    __tablename__ = "employeur"

    # Primary key fields
    emp_mat = Column(Integer, primary_key=True, nullable=False)
    emp_cle = Column(Integer, primary_key=True, nullable=False)

    # Company information - EXACTLY matching your Oracle table
    emp_rais = Column(String(100), nullable=True)  # Company name
    emp_sigle = Column(String(20), nullable=True)  # Company acronym
    emp_activite = Column(String(100), nullable=True)  # Activity
    emp_email = Column(String(100), nullable=True)  # Email
    emp_tel = Column(String(20), nullable=True)  # Phone

    # Date fields - EXACTLY matching your Oracle table
    emp_dtaff = Column(Date, nullable=True)  # Affiliation date
    emp_dtdact = Column(Date, nullable=True)  # Deactivation date
    
    def __repr__(self):
        return f"<Employeur(mat={self.emp_mat}, cle={self.emp_cle}, rais='{self.emp_rais}')>"
    
    def to_dict(self):
        """Convert model to dictionary for JSON serialization"""
        return {
            'emp_mat': self.emp_mat,
            'emp_cle': self.emp_cle,
            'emp_rais': self.emp_rais,
            'emp_sigle': self.emp_sigle,
            'emp_activite': self.emp_activite,
            'emp_email': self.emp_email,
            'emp_tel': self.emp_tel,
            'emp_dtaff': self.emp_dtaff.isoformat() if self.emp_dtaff else None,
            'emp_dtdact': self.emp_dtdact.isoformat() if self.emp_dtdact else None
        }
