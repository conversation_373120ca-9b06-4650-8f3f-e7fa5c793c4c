"""
Service model for CNSS platform
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base
import enum


class ServiceType(str, enum.Enum):
    """Service types enumeration"""
    DECLARATION = "declaration"
    PAYMENT = "payment"
    INQUIRY = "inquiry"
    CERTIFICATE = "certificate"
    REPORT = "report"


class ServiceStatus(str, enum.Enum):
    """Service status enumeration"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    MAINTENANCE = "maintenance"
    DEPRECATED = "deprecated"


class Service(Base):
    """Service model for managing CNSS services"""
    
    __tablename__ = "services"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    code = Column(String(50), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    service_type = Column(Enum(ServiceType), nullable=False)
    status = Column(Enum(ServiceStatus), default=ServiceStatus.ACTIVE, nullable=False)
    
    # API Configuration
    endpoint_url = Column(String(500), nullable=True)
    http_method = Column(String(10), default="POST", nullable=False)
    content_type = Column(String(100), default="application/json", nullable=False)
    
    # Authentication
    requires_auth = Column(Boolean, default=True, nullable=False)
    auth_type = Column(String(50), default="bearer", nullable=True)
    
    # Rate Limiting
    rate_limit_per_minute = Column(Integer, default=60, nullable=True)
    rate_limit_per_hour = Column(Integer, default=1000, nullable=True)
    
    # Documentation
    documentation_url = Column(String(500), nullable=True)
    example_request = Column(Text, nullable=True)
    example_response = Column(Text, nullable=True)
    
    # Versioning
    version = Column(String(20), default="1.0", nullable=False)
    is_latest_version = Column(Boolean, default=True, nullable=False)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Relationships
    creator = relationship("User", backref="created_services")
    
    def __repr__(self):
        return f"<Service(id={self.id}, name='{self.name}', code='{self.code}')>"
    
    @property
    def is_available(self) -> bool:
        """Check if service is available"""
        return self.status == ServiceStatus.ACTIVE
