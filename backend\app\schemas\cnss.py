"""
CNSS-specific Pydantic schemas for request/response validation
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime
from enum import Enum

class GenderEnum(str, Enum):
    MALE = "M"
    FEMALE = "F"

class AssuranceStatusEnum(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"

class PaymentStatusEnum(str, Enum):
    PENDING = "pending"
    PAID = "paid"
    OVERDUE = "overdue"
    CANCELLED = "cancelled"

class BenefitStatusEnum(str, Enum):
    ACTIVE = "active"
    SUSPENDED = "suspended"
    TERMINATED = "terminated"

# Citizen Schemas
class CitizenBase(BaseModel):
    cin: str = Field(..., min_length=8, max_length=8, description="Tunisian CIN (8 digits)")
    first_name: str = Field(..., min_length=2, max_length=100)
    last_name: str = Field(..., min_length=2, max_length=100)
    birth_date: datetime
    birth_place: Optional[str] = None
    gender: Optional[GenderEnum] = None
    address: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None

    @validator('cin')
    def validate_cin(cls, v):
        if not v.isdigit():
            raise ValueError('CIN must contain only digits')
        return v

class CitizenCreate(CitizenBase):
    pass

class CitizenResponse(CitizenBase):
    id: int
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True

# Assurance Record Schemas
class AssuranceRecordBase(BaseModel):
    cnss_number: str = Field(..., max_length=15)
    status: AssuranceStatusEnum = AssuranceStatusEnum.ACTIVE
    start_date: datetime
    end_date: Optional[datetime] = None
    monthly_salary: Optional[float] = Field(None, ge=0)
    contribution_rate: float = Field(0.0925, ge=0, le=1)

class AssuranceRecordCreate(AssuranceRecordBase):
    citizen_id: int
    employer_id: Optional[int] = None

class AssuranceRecordResponse(AssuranceRecordBase):
    id: int
    citizen_id: int
    employer_id: Optional[int]
    last_contribution_date: Optional[datetime]
    total_contributions: float
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Enhanced Employer Schemas
class EmployerBase(BaseModel):
    company_name: str = Field(..., min_length=2, max_length=255)
    registration_number: str = Field(..., max_length=50)
    tax_id: str = Field(..., max_length=50)
    cnss_number: str = Field(..., max_length=20)
    legal_form: Optional[str] = Field(None, max_length=50)
    activity_sector: Optional[str] = Field(None, max_length=100)
    employee_count: int = Field(0, ge=0)
    
    email: Optional[str] = None
    phone: Optional[str] = None
    fax: Optional[str] = None
    
    address_line1: Optional[str] = None
    address_line2: Optional[str] = None
    city: Optional[str] = None
    postal_code: Optional[str] = None
    region: Optional[str] = None
    
    legal_rep_name: Optional[str] = None
    legal_rep_title: Optional[str] = None
    legal_rep_cin: Optional[str] = None

class EmployerCreate(EmployerBase):
    pass

class EmployerResponse(EmployerBase):
    id: int
    is_active: bool
    is_verified: bool
    registration_date: datetime
    last_declaration_date: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Payment Schemas
class ContributionPaymentBase(BaseModel):
    payment_type: str = Field(..., max_length=30)
    period_month: int = Field(..., ge=1, le=12)
    period_year: int = Field(..., ge=2000, le=2100)
    base_salary: float = Field(..., ge=0)
    contribution_amount: float = Field(..., ge=0)
    penalty_amount: float = Field(0.0, ge=0)
    total_amount: float = Field(..., ge=0)
    due_date: datetime
    payment_method: Optional[str] = None
    bank_reference: Optional[str] = None

class ContributionPaymentCreate(ContributionPaymentBase):
    employer_id: int
    assurance_record_id: Optional[int] = None

class ContributionPaymentResponse(ContributionPaymentBase):
    id: int
    employer_id: int
    assurance_record_id: Optional[int]
    payment_reference: str
    payment_date: Optional[datetime]
    status: PaymentStatusEnum
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Benefit Schemas
class BenefitBase(BaseModel):
    benefit_type: str = Field(..., max_length=50)
    benefit_code: Optional[str] = None
    description: Optional[str] = None
    amount: Optional[float] = Field(None, ge=0)
    currency: str = Field("TND", max_length=3)
    frequency: Optional[str] = None
    start_date: datetime
    end_date: Optional[datetime] = None
    eligibility_criteria: Optional[str] = None
    required_contributions_months: Optional[int] = Field(None, ge=0)

class BenefitCreate(BenefitBase):
    citizen_id: int

class BenefitResponse(BenefitBase):
    id: int
    citizen_id: int
    status: BenefitStatusEnum
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Service Schemas
class CNSSServiceBase(BaseModel):
    name: str = Field(..., max_length=200)
    code: str = Field(..., max_length=50)
    description: Optional[str] = None
    category: Optional[str] = None
    endpoint_url: Optional[str] = None
    http_method: str = Field("GET", max_length=10)
    target_audience: Optional[str] = None
    processing_time: Optional[str] = None
    required_documents: Optional[str] = None

class CNSSServiceCreate(CNSSServiceBase):
    pass

class CNSSServiceResponse(CNSSServiceBase):
    id: int
    requires_auth: bool
    is_active: bool
    version: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Response Models for API endpoints
class AssuranceInfoResponse(BaseModel):
    """Response for /assurance-info/{cin} endpoint"""
    citizen: CitizenResponse
    assurance_record: Optional[AssuranceRecordResponse]
    employer: Optional[EmployerResponse]
    status: str
    message: str

class BenefitsResponse(BaseModel):
    """Response for /benefits/{cin} endpoint"""
    citizen: CitizenResponse
    benefits: List[BenefitResponse]
    total_benefits: int
    active_benefits: int

class PaymentHistoryResponse(BaseModel):
    """Response for /payments/{employer_id} endpoint"""
    employer: EmployerResponse
    payments: List[ContributionPaymentResponse]
    total_payments: int
    total_amount: float
    pending_amount: float
    overdue_count: int
