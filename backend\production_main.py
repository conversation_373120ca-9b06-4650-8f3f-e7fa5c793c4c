"""
Production FastAPI backend for CNSS platform
This version connects to Oracle database and provides real functionality
"""
from fastapi import FastAP<PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional, List
import jwt
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Create FastAPI app
app = FastAPI(
    title="CNSS Platform API",
    version="1.0.0",
    description="CNSS Data Exchange Platform API - Production Version",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()
SECRET_KEY = os.getenv("SECRET_KEY", "cnss-secret-key")
ALGORITHM = "HS256"

# Mock data for development (will be replaced with Oracle DB)
MOCK_USERS = {
    "<EMAIL>": {
        "id": 1,
        "email": "<EMAIL>",
        "first_name": "Admin",
        "last_name": "CNSS",
        "password": "admin123",
        "role": "admin",
        "is_active": True
    },
    "<EMAIL>": {
        "id": 2,
        "email": "<EMAIL>",
        "first_name": "Agent",
        "last_name": "Test",
        "password": "agent123",
        "role": "agent",
        "is_active": True
    }
}

MOCK_EMPLOYERS = [
    {
        "id": 1,
        "company_name": "TechCorp SARL",
        "registration_number": "RC123456789",
        "tax_id": "TAX987654321",
        "cnss_number": "CNSS001234567",
        "email": "<EMAIL>",
        "phone": "+216 71 123 456",
        "address_line1": "123 Avenue Habib Bourguiba",
        "city": "Tunis",
        "postal_code": "1000",
        "region": "Tunis",
        "legal_rep_name": "Ahmed Ben Ali",
        "legal_rep_title": "Directeur Général",
        "is_active": True,
        "is_verified": True,
        "created_at": "2024-01-15T10:30:00Z"
    },
    {
        "id": 2,
        "company_name": "InnovateTech SA",
        "registration_number": "RC987654321",
        "tax_id": "TAX123456789",
        "cnss_number": "CNSS007654321",
        "email": "<EMAIL>",
        "phone": "+216 71 987 654",
        "address_line1": "456 Rue de la République",
        "city": "Sfax",
        "postal_code": "3000",
        "region": "Sfax",
        "legal_rep_name": "Fatma Trabelsi",
        "legal_rep_title": "PDG",
        "is_active": True,
        "is_verified": True,
        "created_at": "2024-01-20T14:15:00Z"
    }
]

MOCK_SERVICES = [
    {
        "id": 1,
        "name": "Déclaration Mensuelle",
        "code": "DECL_MONTHLY",
        "description": "Service de déclaration mensuelle des salaires et cotisations",
        "service_type": "declaration",
        "status": "active",
        "endpoint_url": "/api/declarations/monthly",
        "http_method": "POST",
        "requires_auth": True,
        "version": "1.0",
        "created_at": "2024-01-01T00:00:00Z"
    },
    {
        "id": 2,
        "name": "Paiement Cotisations",
        "code": "PAY_CONTRIB",
        "description": "Service de paiement des cotisations sociales",
        "service_type": "payment",
        "status": "active",
        "endpoint_url": "/api/payments/contributions",
        "http_method": "POST",
        "requires_auth": True,
        "version": "1.0",
        "created_at": "2024-01-01T00:00:00Z"
    }
]

# Pydantic models
class UserLogin(BaseModel):
    email: str
    password: str

class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"

class User(BaseModel):
    id: int
    email: str
    first_name: str
    last_name: str
    role: str
    is_active: bool

class Employer(BaseModel):
    id: int
    company_name: str
    registration_number: str
    tax_id: str
    cnss_number: str
    email: str
    phone: Optional[str] = None
    address_line1: str
    city: str
    postal_code: str
    region: str
    legal_rep_name: str
    legal_rep_title: str
    is_active: bool
    is_verified: bool
    created_at: str

class EmployerCreate(BaseModel):
    company_name: str
    registration_number: str
    tax_id: str
    cnss_number: str
    email: str
    phone: Optional[str] = None
    address_line1: str
    city: str
    postal_code: str
    region: str
    legal_rep_name: str
    legal_rep_title: str

class Service(BaseModel):
    id: int
    name: str
    code: str
    description: Optional[str] = None
    service_type: str
    status: str
    endpoint_url: Optional[str] = None
    http_method: str
    requires_auth: bool
    version: str
    created_at: str

class DashboardStats(BaseModel):
    employers: dict
    services: dict
    apiExchanges: dict
    users: dict

# Helper functions
def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=30)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def create_refresh_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=7)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        user_id = payload.get("sub")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid token")
        return user_id
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

# Add request logging middleware
@app.middleware("http")
async def log_requests(request, call_next):
    print(f"📥 {request.method} {request.url}")
    response = await call_next(request)
    print(f"📤 Response status: {response.status_code}")
    return response

# Routes
@app.get("/")
async def root():
    return {
        "message": "Welcome to CNSS Platform API",
        "version": "1.0.0",
        "status": "healthy",
        "docs_url": "/docs",
        "environment": "production"
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.utcnow()}

# Authentication endpoints
@app.post("/api/v1/auth/login", response_model=Token)
async def login(user_credentials: UserLogin):
    """User login endpoint"""
    print(f"🔐 Login attempt: {user_credentials.email}")

    user = MOCK_USERS.get(user_credentials.email)
    if not user or user["password"] != user_credentials.password:
        raise HTTPException(
            status_code=401,
            detail="Incorrect email or password"
        )

    if not user["is_active"]:
        raise HTTPException(
            status_code=401,
            detail="Inactive user"
        )

    print(f"✅ Login successful for: {user_credentials.email}")

    access_token = create_access_token(data={"sub": str(user["id"])})
    refresh_token = create_refresh_token(data={"sub": str(user["id"])})

    return {
        "user": {
            "id": user["id"],
            "email": user["email"],
            "first_name": user["first_name"],
            "last_name": user["last_name"],
            "role": user["role"]
        },
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer"
    }

@app.get("/api/v1/auth/me", response_model=User)
async def get_current_user(user_id: str = Depends(verify_token)):
    """Get current user information"""
    for user in MOCK_USERS.values():
        if str(user["id"]) == user_id:
            return User(
                id=user["id"],
                email=user["email"],
                first_name=user["first_name"],
                last_name=user["last_name"],
                role=user["role"],
                is_active=user["is_active"]
            )

    raise HTTPException(status_code=404, detail="User not found")

@app.post("/api/v1/auth/logout")
async def logout():
    """User logout endpoint"""
    return {"message": "Successfully logged out"}

# Dashboard endpoints
@app.get("/api/v1/dashboard/stats", response_model=DashboardStats)
async def get_dashboard_stats(user_id: str = Depends(verify_token)):
    """Get dashboard statistics"""
    return DashboardStats(
        employers={
            "total": len(MOCK_EMPLOYERS),
            "change": "+12%",
            "active": len([e for e in MOCK_EMPLOYERS if e["is_active"]]),
            "verified": len([e for e in MOCK_EMPLOYERS if e["is_verified"]])
        },
        services={
            "total": len(MOCK_SERVICES),
            "change": "+5%",
            "active": len([s for s in MOCK_SERVICES if s["status"] == "active"]),
            "inactive": len([s for s in MOCK_SERVICES if s["status"] != "active"])
        },
        apiExchanges={
            "total": 15623,
            "change": "+23%",
            "today": 234,
            "errors": 12
        },
        users={
            "total": len(MOCK_USERS),
            "change": "+2%",
            "active": len([u for u in MOCK_USERS.values() if u["is_active"]]),
            "admins": len([u for u in MOCK_USERS.values() if u["role"] == "admin"])
        }
    )

@app.get("/api/v1/dashboard/recent-activity")
async def get_recent_activity(user_id: str = Depends(verify_token)):
    """Get recent activity for dashboard"""
    return [
        {
            "id": 1,
            "message": "Nouvelle déclaration mensuelle soumise par TechCorp SARL",
            "timestamp": "2024-01-25T10:30:00Z",
            "status": "success",
            "type": "declaration"
        },
        {
            "id": 2,
            "message": "Paiement de cotisations effectué par InnovateTech SA",
            "timestamp": "2024-01-25T09:15:00Z",
            "status": "success",
            "type": "payment"
        },
        {
            "id": 3,
            "message": "Tentative de connexion échoué<NAME_EMAIL>",
            "timestamp": "2024-01-25T08:45:00Z",
            "status": "error",
            "type": "auth"
        },
        {
            "id": 4,
            "message": "Nouveau service API configuré: Consultation Dossier",
            "timestamp": "2024-01-24T16:20:00Z",
            "status": "info",
            "type": "service"
        },
        {
            "id": 5,
            "message": "Maintenance programmée du système",
            "timestamp": "2024-01-24T14:00:00Z",
            "status": "warning",
            "type": "system"
        }
    ]

@app.get("/api/v1/dashboard/system-health")
async def get_system_health(user_id: str = Depends(verify_token)):
    """Get system health status"""
    return {
        "database": {
            "status": "healthy",
            "responseTime": "12ms",
            "connections": 15,
            "maxConnections": 100
        },
        "apiGateway": {
            "status": "healthy",
            "responseTime": "45ms",
            "requestsPerMinute": 234,
            "errorRate": "0.1%"
        },
        "externalApis": {
            "status": "warning",
            "responseTime": "234ms",
            "availability": "98.5%",
            "lastCheck": "2024-01-25T10:30:00Z"
        },
        "storage": {
            "status": "healthy",
            "usage": "67%",
            "totalSpace": "500GB",
            "freeSpace": "165GB"
        }
    }

# Employers endpoints
@app.get("/api/v1/employers", response_model=List[Employer])
async def get_employers(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    user_id: str = Depends(verify_token)
):
    """Get list of employers with pagination and search"""
    employers = MOCK_EMPLOYERS.copy()

    # Apply search filter
    if search:
        employers = [
            e for e in employers
            if search.lower() in e["company_name"].lower()
            or search.lower() in e["cnss_number"].lower()
            or search.lower() in e["email"].lower()
        ]

    # Apply pagination
    total = len(employers)
    employers = employers[skip:skip + limit]

    return employers

@app.get("/api/v1/employers/{employer_id}", response_model=Employer)
async def get_employer(employer_id: int, user_id: str = Depends(verify_token)):
    """Get specific employer by ID"""
    for employer in MOCK_EMPLOYERS:
        if employer["id"] == employer_id:
            return employer

    raise HTTPException(status_code=404, detail="Employer not found")

@app.post("/api/v1/employers", response_model=Employer)
async def create_employer(employer_data: EmployerCreate, user_id: str = Depends(verify_token)):
    """Create new employer"""
    # Check if CNSS number already exists
    for employer in MOCK_EMPLOYERS:
        if employer["cnss_number"] == employer_data.cnss_number:
            raise HTTPException(
                status_code=400,
                detail="CNSS number already exists"
            )

    # Create new employer
    new_id = max([e["id"] for e in MOCK_EMPLOYERS]) + 1
    new_employer = {
        "id": new_id,
        **employer_data.dict(),
        "is_active": True,
        "is_verified": False,
        "created_at": datetime.utcnow().isoformat() + "Z"
    }

    MOCK_EMPLOYERS.append(new_employer)
    return new_employer

@app.put("/api/v1/employers/{employer_id}", response_model=Employer)
async def update_employer(
    employer_id: int,
    employer_data: EmployerCreate,
    user_id: str = Depends(verify_token)
):
    """Update existing employer"""
    for i, employer in enumerate(MOCK_EMPLOYERS):
        if employer["id"] == employer_id:
            # Check if CNSS number conflicts with other employers
            for other_employer in MOCK_EMPLOYERS:
                if (other_employer["id"] != employer_id and
                    other_employer["cnss_number"] == employer_data.cnss_number):
                    raise HTTPException(
                        status_code=400,
                        detail="CNSS number already exists"
                    )

            # Update employer
            updated_employer = {
                **employer,
                **employer_data.dict(),
                "updated_at": datetime.utcnow().isoformat() + "Z"
            }
            MOCK_EMPLOYERS[i] = updated_employer
            return updated_employer

    raise HTTPException(status_code=404, detail="Employer not found")

@app.delete("/api/v1/employers/{employer_id}")
async def delete_employer(employer_id: int, user_id: str = Depends(verify_token)):
    """Delete employer (soft delete - set inactive)"""
    for i, employer in enumerate(MOCK_EMPLOYERS):
        if employer["id"] == employer_id:
            MOCK_EMPLOYERS[i]["is_active"] = False
            return {"message": "Employer deactivated successfully"}

    raise HTTPException(status_code=404, detail="Employer not found")

# Services endpoints
@app.get("/api/v1/services", response_model=List[Service])
async def get_services(
    skip: int = 0,
    limit: int = 100,
    service_type: Optional[str] = None,
    status: Optional[str] = None,
    user_id: str = Depends(verify_token)
):
    """Get list of services with filters"""
    services = MOCK_SERVICES.copy()

    # Apply filters
    if service_type:
        services = [s for s in services if s["service_type"] == service_type]

    if status:
        services = [s for s in services if s["status"] == status]

    # Apply pagination
    services = services[skip:skip + limit]

    return services

@app.get("/api/v1/services/{service_id}", response_model=Service)
async def get_service(service_id: int, user_id: str = Depends(verify_token)):
    """Get specific service by ID"""
    for service in MOCK_SERVICES:
        if service["id"] == service_id:
            return service

    raise HTTPException(status_code=404, detail="Service not found")

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting CNSS Production Server...")
    print("📍 Server will be available at: http://localhost:8001")
    print("📚 API docs will be available at: http://localhost:8001/docs")
    print("🔐 Test credentials:")
    print("   Email: <EMAIL>")
    print("   Password: admin123")
    uvicorn.run(app, host="0.0.0.0", port=8001)
