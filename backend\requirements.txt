# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
cx-Oracle==8.3.0
sqlalchemy==2.0.23
alembic==1.12.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Environment & Configuration
python-dotenv==1.0.0
pydantic-settings==2.0.3

# HTTP Client
httpx==0.25.2
requests==2.31.0

# Validation & Serialization
pydantic==2.5.0
email-validator==2.1.0

# CORS
fastapi-cors==0.0.6

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0

# Logging
structlog==23.2.0

# Background Tasks
celery==5.3.4
redis==5.0.1

# File handling
python-magic==0.4.27
Pillow==10.1.0

# Date/Time
python-dateutil==2.8.2

# Utilities
click==8.1.7
rich==13.7.0
