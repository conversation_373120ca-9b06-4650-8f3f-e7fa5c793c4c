"""
Simplified FastAP<PERSON> backend for CNSS platform - Development version
This version works without Oracle database for quick testing
"""
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr
from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional
import jwt

# Create FastAPI app
app = FastAPI(
    title="CNSS Platform API",
    version="1.0.0",
    description="CNSS Data Exchange Platform API - Development Version"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()
SECRET_KEY = "your-secret-key-for-development"
ALGORITHM = "HS256"

# Mock users database
MOCK_USERS = {
    "<EMAIL>": {
        "id": 1,
        "email": "<EMAIL>",
        "first_name": "Admin",
        "last_name": "CNSS",
        "password": "admin123",  # In real app, this would be hashed
        "role": "admin",
        "is_active": True
    },
    "<EMAIL>": {
        "id": 2,
        "email": "<EMAIL>",
        "first_name": "Agent",
        "last_name": "Test",
        "password": "agent123",  # In real app, this would be hashed
        "role": "agent",
        "is_active": True
    }
}

# Pydantic models
class UserLogin(BaseModel):
    email: EmailStr
    password: str

class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"

class User(BaseModel):
    id: int
    email: str
    first_name: str
    last_name: str
    role: str
    is_active: bool

# Helper functions
def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=30)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def create_refresh_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=7)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        user_id = payload.get("sub")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid token")
        return user_id
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

# Routes
@app.get("/")
async def root():
    return {
        "message": "Welcome to CNSS Platform API",
        "version": "1.0.0",
        "status": "healthy",
        "docs_url": "/docs"
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.utcnow()}

@app.post("/api/v1/auth/login", response_model=Token)
async def login(user_credentials: UserLogin):
    """User login endpoint"""
    
    # Check if user exists
    user = MOCK_USERS.get(user_credentials.email)
    if not user:
        raise HTTPException(
            status_code=401,
            detail="Incorrect email or password"
        )
    
    # Check password (in real app, use password hashing)
    if user["password"] != user_credentials.password:
        raise HTTPException(
            status_code=401,
            detail="Incorrect email or password"
        )
    
    # Check if user is active
    if not user["is_active"]:
        raise HTTPException(
            status_code=401,
            detail="Inactive user"
        )
    
    # Create tokens
    access_token = create_access_token(data={"sub": str(user["id"])})
    refresh_token = create_refresh_token(data={"sub": str(user["id"])})
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer"
    }

@app.get("/api/v1/auth/me", response_model=User)
async def get_current_user(user_id: str = Depends(verify_token)):
    """Get current user information"""
    
    # Find user by ID
    for user in MOCK_USERS.values():
        if str(user["id"]) == user_id:
            return User(
                id=user["id"],
                email=user["email"],
                first_name=user["first_name"],
                last_name=user["last_name"],
                role=user["role"],
                is_active=user["is_active"]
            )
    
    raise HTTPException(status_code=404, detail="User not found")

@app.post("/api/v1/auth/logout")
async def logout():
    """User logout endpoint"""
    return {"message": "Successfully logged out"}

# Mock dashboard data
@app.get("/api/v1/dashboard/stats")
async def get_dashboard_stats():
    """Get dashboard statistics"""
    return {
        "employers": {"total": 1247, "change": "+12%"},
        "services": {"total": 89, "change": "+5%"},
        "apiExchanges": {"total": 15623, "change": "+23%"},
        "users": {"total": 24, "change": "+2%"}
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
