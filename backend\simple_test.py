#!/usr/bin/env python3
"""
Simple Oracle connection test
"""
import oracledb

# Test different connection methods
connection_strings = [
    # Method 1: Simple connection string
    "cnss_user/cnss_password@localhost:1521/xepdb1",
    
    # Method 2: Using service name
    "cnss_user/cnss_password@(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=localhost)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=xepdb1)))",
    
    # Method 3: Using XEPDB1 (uppercase)
    "cnss_user/cnss_password@localhost:1521/XEPDB1",
    
    # Method 4: Direct connection parameters
    None  # Will use connect() with parameters
]

print("🔍 Testing Oracle Connection Methods...")
print("=" * 50)

for i, conn_str in enumerate(connection_strings, 1):
    print(f"\n📊 Method {i}:")
    
    try:
        if conn_str is None:
            # Method 4: Direct parameters
            print("   Using direct parameters...")
            connection = oracledb.connect(
                user="cnss_user",
                password="cnss_password",
                host="localhost",
                port=1521,
                service_name="xepdb1"
            )
        else:
            print(f"   Connection string: {conn_str}")
            connection = oracledb.connect(conn_str)
        
        # Test the connection
        cursor = connection.cursor()
        cursor.execute("SELECT 'Hello from Oracle!' FROM dual")
        result = cursor.fetchone()
        print(f"   ✅ SUCCESS: {result[0]}")
        
        # Test table access
        cursor.execute("SELECT COUNT(*) FROM employeur")
        count = cursor.fetchone()[0]
        print(f"   📊 EMPLOYEUR table has {count} records")
        
        cursor.close()
        connection.close()
        
        print(f"   🎉 Method {i} WORKS!")
        break
        
    except Exception as e:
        print(f"   ❌ FAILED: {e}")

print("\n" + "=" * 50)
print("Test completed!")
