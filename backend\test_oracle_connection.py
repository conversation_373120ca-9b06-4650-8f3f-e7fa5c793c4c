#!/usr/bin/env python3
"""
Test script to verify Oracle database connection and table access
Run this script to test your Oracle setup before starting the main application
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    import oracledb
    from sqlalchemy import create_engine, text
    from app.core.config import settings
    from app.models.employeur import Employeur
    from app.models.assure import Assure
    from app.models.beneficiaire import Beneficiaire
    from app.core.database import SessionLocal
    
    print("✅ All imports successful")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please install required packages: pip install -r requirements.txt")
    sys.exit(1)

def test_oracle_connection():
    """Test basic Oracle connection"""
    print("\n🔍 Testing Oracle connection...")
    print(f"📊 Database URL: {settings.database_url_sync}")
    print(f"🏠 Host: {settings.DB_HOST}:{settings.DB_PORT}")
    print(f"👤 User: {settings.DB_USER}")
    print(f"🔧 Service: {settings.DB_SERVICE_NAME}")

    try:
        # Test basic connection
        engine = create_engine(settings.database_url_sync)

        with engine.connect() as conn:
            # Test basic query
            result = conn.execute(text("SELECT 'Hello from Oracle!' as message FROM dual"))
            message = result.fetchone()[0]
            print(f"✅ Connection successful: {message}")

            # Test user info
            result = conn.execute(text("SELECT USER FROM dual"))
            user = result.fetchone()[0]
            print(f"👤 Connected as user: {user}")

            # Test tablespace
            result = conn.execute(text("SELECT default_tablespace FROM user_users"))
            tablespace = result.fetchone()[0]
            print(f"🗄️  Default tablespace: {tablespace}")

            # List existing tables
            result = conn.execute(text("SELECT table_name FROM user_tables ORDER BY table_name"))
            tables = result.fetchall()

            if tables:
                print(f"📋 Existing tables ({len(tables)}):")
                for table in tables:
                    print(f"   - {table[0]}")

                    # Show table structure
                    table_name = table[0]
                    result = conn.execute(text(f"""
                        SELECT column_name, data_type, nullable
                        FROM user_tab_columns
                        WHERE table_name = '{table_name}'
                        ORDER BY column_id
                    """))
                    columns = result.fetchall()
                    for col in columns:
                        nullable = "NULL" if col[2] == "Y" else "NOT NULL"
                        print(f"     └─ {col[0]} ({col[1]}) {nullable}")
                    print()
                return True
            else:
                print("📋 No tables found in schema")
                print("\n💡 You need to create the CNSS tables.")
                return True

    except Exception as e:
        print(f"❌ Oracle connection failed: {e}")
        print(f"❌ Error type: {type(e).__name__}")
        print("\n🔧 Troubleshooting steps:")
        print("1. Make sure Oracle container is running:")
        print("   docker ps")
        print("2. Test Oracle connection manually:")
        print("   docker exec -it oracle-xe sqlplus cnss_user/cnss_password@XEPDB1")
        print("3. Check if service is available:")
        print("   docker exec -it oracle-xe lsnrctl status")
        print("4. Verify your credentials match what you created")
        return False

def test_table_access():
    """Test access to CNSS tables"""
    print("\n🔍 Testing table access...")
    
    try:
        db = SessionLocal()
        
        # Test employeur table
        try:
            employeur_count = db.query(Employeur).count()
            print(f"✅ Employeur table accessible - {employeur_count} records")
        except Exception as e:
            print(f"❌ Employeur table error: {e}")
            return False
        
        # Test assure table
        try:
            assure_count = db.query(Assure).count()
            print(f"✅ Assure table accessible - {assure_count} records")
        except Exception as e:
            print(f"❌ Assure table error: {e}")
            return False
        
        # Test beneficiaire table
        try:
            beneficiaire_count = db.query(Beneficiaire).count()
            print(f"✅ Beneficiaire table accessible - {beneficiaire_count} records")
        except Exception as e:
            print(f"❌ Beneficiaire table error: {e}")
            return False
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database session error: {e}")
        return False

def test_sample_queries():
    """Test sample queries on the tables"""
    print("\n🔍 Testing sample queries...")
    
    try:
        db = SessionLocal()
        
        # Test employeur query
        employeurs = db.query(Employeur).limit(3).all()
        print(f"✅ Retrieved {len(employeurs)} employeurs")
        for emp in employeurs:
            print(f"   - {emp.emp_mat}/{emp.emp_cle}: {emp.emp_rais or 'N/A'}")
        
        # Test assure query
        assures = db.query(Assure).limit(3).all()
        print(f"✅ Retrieved {len(assures)} assurés")
        for ass in assures:
            print(f"   - {ass.ass_mat}/{ass.ass_cle}: Employer {ass.emp_mat or 'N/A'}")
        
        # Test beneficiaire query
        beneficiaires = db.query(Beneficiaire).limit(3).all()
        print(f"✅ Retrieved {len(beneficiaires)} bénéficiaires")
        for ben in beneficiaires:
            print(f"   - {ben.ben_iducnss}: {ben.ben_nom or 'N/A'} {ben.ben_prenom or 'N/A'}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Sample queries error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 CNSS Oracle Database Connection Test")
    print("=" * 50)
    
    print(f"Database URL: {settings.database_url_sync}")
    print(f"Host: {settings.DB_HOST}:{settings.DB_PORT}")
    print(f"Service: {settings.DB_SERVICE_NAME}")
    print(f"User: {settings.DB_USER}")
    
    # Run tests
    tests = [
        ("Oracle Connection", test_oracle_connection),
        ("Table Access", test_table_access),
        ("Sample Queries", test_sample_queries)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! Your Oracle setup is ready.")
        print("You can now start the FastAPI server with:")
        print("   python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8001")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")
        print("Refer to SETUP_INSTRUCTIONS.md for troubleshooting.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
