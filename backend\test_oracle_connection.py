#!/usr/bin/env python3
"""
Test script to verify Oracle database connection and table access
Run this script to test your Oracle setup before starting the main application
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    import oracledb
    from sqlalchemy import create_engine, text
    from app.core.config import settings
    from app.models.employeur import Employeur
    from app.models.assure import Assure
    from app.models.beneficiaire import Beneficiaire
    from app.core.database import SessionLocal
    
    print("✅ All imports successful")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please install required packages: pip install -r requirements.txt")
    sys.exit(1)

def test_oracle_connection():
    """Test basic Oracle connection"""
    print("\n🔍 Testing Oracle connection...")
    
    try:
        # Test basic connection
        engine = create_engine(settings.database_url_sync)
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1 FROM DUAL"))
            row = result.fetchone()
            if row and row[0] == 1:
                print("✅ Oracle connection successful")
                return True
            else:
                print("❌ Oracle connection failed - unexpected result")
                return False
                
    except Exception as e:
        print(f"❌ Oracle connection failed: {e}")
        print("\nTroubleshooting tips:")
        print("1. Check Oracle service is running")
        print("2. Verify connection parameters in backend/app/core/config.py")
        print("3. Ensure Oracle client is installed")
        print("4. Check firewall settings")
        return False

def test_table_access():
    """Test access to CNSS tables"""
    print("\n🔍 Testing table access...")
    
    try:
        db = SessionLocal()
        
        # Test employeur table
        try:
            employeur_count = db.query(Employeur).count()
            print(f"✅ Employeur table accessible - {employeur_count} records")
        except Exception as e:
            print(f"❌ Employeur table error: {e}")
            return False
        
        # Test assure table
        try:
            assure_count = db.query(Assure).count()
            print(f"✅ Assure table accessible - {assure_count} records")
        except Exception as e:
            print(f"❌ Assure table error: {e}")
            return False
        
        # Test beneficiaire table
        try:
            beneficiaire_count = db.query(Beneficiaire).count()
            print(f"✅ Beneficiaire table accessible - {beneficiaire_count} records")
        except Exception as e:
            print(f"❌ Beneficiaire table error: {e}")
            return False
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database session error: {e}")
        return False

def test_sample_queries():
    """Test sample queries on the tables"""
    print("\n🔍 Testing sample queries...")
    
    try:
        db = SessionLocal()
        
        # Test employeur query
        employeurs = db.query(Employeur).limit(3).all()
        print(f"✅ Retrieved {len(employeurs)} employeurs")
        for emp in employeurs:
            print(f"   - {emp.emp_mat}/{emp.emp_cle}: {emp.emp_rais or 'N/A'}")
        
        # Test assure query
        assures = db.query(Assure).limit(3).all()
        print(f"✅ Retrieved {len(assures)} assurés")
        for ass in assures:
            print(f"   - {ass.ass_mat}/{ass.ass_cle}: Employer {ass.emp_mat or 'N/A'}")
        
        # Test beneficiaire query
        beneficiaires = db.query(Beneficiaire).limit(3).all()
        print(f"✅ Retrieved {len(beneficiaires)} bénéficiaires")
        for ben in beneficiaires:
            print(f"   - {ben.ben_iducnss}: {ben.ben_nom or 'N/A'} {ben.ben_prenom or 'N/A'}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Sample queries error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 CNSS Oracle Database Connection Test")
    print("=" * 50)
    
    print(f"Database URL: {settings.database_url_sync}")
    print(f"Host: {settings.DB_HOST}:{settings.DB_PORT}")
    print(f"Service: {settings.DB_SERVICE_NAME}")
    print(f"User: {settings.DB_USER}")
    
    # Run tests
    tests = [
        ("Oracle Connection", test_oracle_connection),
        ("Table Access", test_table_access),
        ("Sample Queries", test_sample_queries)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! Your Oracle setup is ready.")
        print("You can now start the FastAPI server with:")
        print("   python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8001")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")
        print("Refer to SETUP_INSTRUCTIONS.md for troubleshooting.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
