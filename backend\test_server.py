"""
Ultra-simple test server for CNSS platform
This will definitely work for testing login
"""
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Create FastAPI app
app = FastAPI(title="CNSS Test API")

# Add CORS - Allow all origins for testing
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for testing
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add request logging middleware
@app.middleware("http")
async def log_requests(request, call_next):
    print(f"📥 {request.method} {request.url}")
    print(f"📋 Headers: {dict(request.headers)}")
    response = await call_next(request)
    print(f"📤 Response status: {response.status_code}")
    return response

# Simple models
class LoginRequest(BaseModel):
    email: str
    password: str

class LoginResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str

class UserResponse(BaseModel):
    id: int
    email: str
    first_name: str
    last_name: str
    role: str

# Test users
TEST_USERS = {
    "<EMAIL>": {
        "id": 1,
        "email": "<EMAIL>",
        "first_name": "Admin",
        "last_name": "CNSS",
        "password": "admin123",
        "role": "admin"
    },
    "<EMAIL>": {
        "id": 2,
        "email": "<EMAIL>",
        "first_name": "Agent",
        "last_name": "Test",
        "password": "agent123",
        "role": "agent"
    }
}

@app.get("/")
def read_root():
    return {"message": "CNSS Test API is running!", "status": "OK"}

@app.get("/health")
def health_check():
    return {"status": "healthy"}

@app.post("/api/v1/auth/login")
def login(login_data: LoginRequest):
    print(f"🔐 Login attempt: {login_data.email}")  # Debug print

    # Check if user exists
    if login_data.email not in TEST_USERS:
        print(f"❌ User not found: {login_data.email}")
        raise HTTPException(status_code=401, detail="Incorrect email or password")

    user = TEST_USERS[login_data.email]

    # Check password
    if user["password"] != login_data.password:
        print(f"❌ Wrong password for: {login_data.email}")
        raise HTTPException(status_code=401, detail="Incorrect email or password")

    print(f"✅ Login successful for: {login_data.email}")

    # Return success response in the exact format the frontend expects
    return {
        "user": {
            "id": user["id"],
            "email": user["email"],
            "first_name": user["first_name"],
            "last_name": user["last_name"],
            "role": user["role"]
        },
        "access_token": "test-token-123",
        "refresh_token": "refresh-token-456",
        "token_type": "bearer"
    }

@app.get("/api/v1/auth/me")
def get_current_user():
    # Return test user for now
    return {
        "id": 1,
        "email": "<EMAIL>",
        "first_name": "Admin",
        "last_name": "CNSS",
        "role": "admin"
    }

@app.post("/api/v1/auth/logout")
def logout():
    return {"message": "Successfully logged out"}

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting CNSS Test Server...")
    print("📍 Server will be available at: http://localhost:8001")
    print("📚 API docs will be available at: http://localhost:8001/docs")
    print("🔐 Test credentials:")
    print("   Email: <EMAIL>")
    print("   Password: admin123")
    uvicorn.run(app, host="0.0.0.0", port=8001)
