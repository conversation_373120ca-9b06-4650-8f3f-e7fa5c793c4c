-- ========================================
-- CNSS Platform - Oracle Database Tables
-- ========================================
-- Run this script as cnss_user to create all required tables
-- Usage: sqlplus cnss_user/cnss_password@XEPDB1 @create_cnss_tables.sql

-- Connect as cnss_user (if not already connected)
-- CONNECT cnss_user/cnss_password@XEPDB1;

-- Drop tables if they exist (for clean setup)
BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE beneficiaire CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE assure CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'DROP TABLE employeur CASCADE CONSTRAINTS';
EXCEPTION
    WHEN OTHERS THEN NULL;
END;
/

-- ========================================
-- 1. EMPLOYEUR Table
-- ========================================
CREATE TABLE employeur (
    emp_mat NUMBER(8) NOT NULL,
    emp_cle NUMBER(2) NOT NULL,
    emp_rais VARCHAR2(100),
    emp_sigle VARCHAR2(20),
    emp_activite VARCHAR2(100),
    emp_email VARCHAR2(100),
    emp_tel VARCHAR2(20),
    emp_dtaff DATE,
    emp_dtdact DATE,
    emp_adresse VARCHAR2(200),
    emp_ville VARCHAR2(50),
    emp_code_postal VARCHAR2(10),
    emp_statut VARCHAR2(20) DEFAULT 'ACTIF',
    created_at DATE DEFAULT SYSDATE,
    updated_at DATE DEFAULT SYSDATE,
    CONSTRAINT pk_employeur PRIMARY KEY (emp_mat, emp_cle),
    CONSTRAINT chk_emp_statut CHECK (emp_statut IN ('ACTIF', 'INACTIF', 'SUSPENDU'))
);

-- ========================================
-- 2. ASSURE Table
-- ========================================
CREATE TABLE assure (
    ass_mat NUMBER(8) NOT NULL,
    ass_cle NUMBER(2) NOT NULL,
    emp_mat NUMBER(8),
    emp_cle NUMBER(2),
    ass_nom VARCHAR2(50),
    ass_prenom VARCHAR2(50),
    ass_dteff DATE,
    ass_dtimmat DATE,
    ass_rib VARCHAR2(24),
    ass_cnrps VARCHAR2(20),
    ass_cin VARCHAR2(8),
    ass_dtnaiss DATE,
    ass_sexe NUMBER(1),
    ass_tel VARCHAR2(20),
    ass_email VARCHAR2(100),
    etat_chargement VARCHAR2(20) DEFAULT 'ACTIF',
    created_at DATE DEFAULT SYSDATE,
    updated_at DATE DEFAULT SYSDATE,
    CONSTRAINT pk_assure PRIMARY KEY (ass_mat, ass_cle),
    CONSTRAINT fk_assure_employeur FOREIGN KEY (emp_mat, emp_cle) 
        REFERENCES employeur(emp_mat, emp_cle),
    CONSTRAINT chk_ass_sexe CHECK (ass_sexe IN (1, 2)),
    CONSTRAINT chk_ass_etat CHECK (etat_chargement IN ('ACTIF', 'INACTIF', 'SUSPENDU'))
);

-- ========================================
-- 3. BENEFICIAIRE Table
-- ========================================
CREATE TABLE beneficiaire (
    ben_mat NUMBER(8) NOT NULL,
    ben_cle NUMBER(2) NOT NULL,
    ben_iducnss VARCHAR2(20),
    ben_nom VARCHAR2(50),
    ben_prenom VARCHAR2(50),
    ben_nom_ar VARCHAR2(50),
    ben_prn_ar VARCHAR2(50),
    ben_email VARCHAR2(100),
    ben_tel VARCHAR2(20),
    ben_numid VARCHAR2(20),
    ben_typid NUMBER(1) DEFAULT 0,
    ben_dtnais DATE,
    ben_dtdeces DATE,
    ben_sexe NUMBER(1),
    ben_type NUMBER(2) DEFAULT 1,
    ben_statut VARCHAR2(20) DEFAULT 'ACTIF',
    created_at DATE DEFAULT SYSDATE,
    updated_at DATE DEFAULT SYSDATE,
    CONSTRAINT pk_beneficiaire PRIMARY KEY (ben_mat, ben_cle),
    CONSTRAINT chk_ben_sexe CHECK (ben_sexe IN (1, 2)),
    CONSTRAINT chk_ben_typid CHECK (ben_typid IN (0, 1, 2)),
    CONSTRAINT chk_ben_statut CHECK (ben_statut IN ('ACTIF', 'INACTIF', 'DECEDE'))
);

-- ========================================
-- Create Indexes for Performance
-- ========================================
CREATE INDEX idx_employeur_email ON employeur(emp_email);
CREATE INDEX idx_employeur_statut ON employeur(emp_statut);
CREATE INDEX idx_assure_emp ON assure(emp_mat, emp_cle);
CREATE INDEX idx_assure_cin ON assure(ass_cin);
CREATE INDEX idx_assure_email ON assure(ass_email);
CREATE INDEX idx_beneficiaire_iducnss ON beneficiaire(ben_iducnss);
CREATE INDEX idx_beneficiaire_email ON beneficiaire(ben_email);
CREATE INDEX idx_beneficiaire_numid ON beneficiaire(ben_numid);

-- ========================================
-- Insert Sample Data
-- ========================================

-- Sample Employeurs
INSERT INTO employeur (emp_mat, emp_cle, emp_rais, emp_sigle, emp_activite, emp_email, emp_tel, emp_dtaff, emp_adresse, emp_ville, emp_code_postal) VALUES
(12345678, 12, 'SOCIETE GENERALE TUNISIE', 'SGT', 'BANQUE', '<EMAIL>', '71123456', DATE '2020-01-15', 'Avenue Habib Bourguiba', 'Tunis', '1000');

INSERT INTO employeur (emp_mat, emp_cle, emp_rais, emp_sigle, emp_activite, emp_email, emp_tel, emp_dtaff, emp_adresse, emp_ville, emp_code_postal) VALUES
(87654321, 34, 'TUNISIE TELECOM', 'TT', 'TELECOMMUNICATIONS', '<EMAIL>', '71987654', DATE '2019-03-20', 'Rue de la Liberté', 'Tunis', '1002');

INSERT INTO employeur (emp_mat, emp_cle, emp_rais, emp_sigle, emp_activite, emp_email, emp_tel, emp_dtaff, emp_adresse, emp_ville, emp_code_postal) VALUES
(11223344, 56, 'ORANGE TUNISIE', 'OT', 'TELECOMMUNICATIONS', '<EMAIL>', '71555666', DATE '2018-06-10', 'Avenue Mohamed V', 'Tunis', '1001');

INSERT INTO employeur (emp_mat, emp_cle, emp_rais, emp_sigle, emp_activite, emp_email, emp_tel, emp_dtaff, emp_adresse, emp_ville, emp_code_postal) VALUES
(99887766, 78, 'BANQUE CENTRALE', 'BCT', 'BANQUE', '<EMAIL>', '71777888', DATE '2015-01-01', 'Place de l''Indépendance', 'Tunis', '1000');

-- Sample Assurés
INSERT INTO assure (ass_mat, ass_cle, emp_mat, emp_cle, ass_nom, ass_prenom, ass_dteff, ass_dtimmat, ass_rib, ass_cnrps, ass_cin, ass_dtnaiss, ass_sexe, ass_tel, ass_email) VALUES
(11111111, 11, 12345678, 12, 'BEN SALEM', 'MOHAMED', DATE '2020-02-01', DATE '2020-01-20', 'TN59 1234 5678 9012 3456 7890', 'CNRPS123', '12345678', DATE '1985-05-15', 1, '98123456', '<EMAIL>');

INSERT INTO assure (ass_mat, ass_cle, emp_mat, emp_cle, ass_nom, ass_prenom, ass_dteff, ass_dtimmat, ass_rib, ass_cnrps, ass_cin, ass_dtnaiss, ass_sexe, ass_tel, ass_email) VALUES
(22222222, 22, 87654321, 34, 'TRABELSI', 'FATMA', DATE '2019-04-01', DATE '2019-03-25', 'TN59 9876 5432 1098 7654 3210', 'CNRPS456', '87654321', DATE '1990-08-22', 2, '97987654', '<EMAIL>');

INSERT INTO assure (ass_mat, ass_cle, emp_mat, emp_cle, ass_nom, ass_prenom, ass_dteff, ass_dtimmat, ass_rib, ass_cnrps, ass_cin, ass_dtnaiss, ass_sexe, ass_tel, ass_email) VALUES
(33333333, 33, 11223344, 56, 'GHARBI', 'AHMED', DATE '2018-07-15', DATE '2018-06-20', 'TN59 1111 2222 3333 4444 5555', 'CNRPS789', '11223344', DATE '1988-12-03', 1, '96111222', '<EMAIL>');

INSERT INTO assure (ass_mat, ass_cle, emp_mat, emp_cle, ass_nom, ass_prenom, ass_dteff, ass_dtimmat, ass_rib, ass_cnrps, ass_cin, ass_dtnaiss, ass_sexe, ass_tel, ass_email) VALUES
(44444444, 44, 99887766, 78, 'SASSI', 'LEILA', DATE '2015-02-01', DATE '2015-01-15', 'TN59 6666 7777 8888 9999 0000', 'CNRPS012', '55667788', DATE '1992-03-18', 2, '95333444', '<EMAIL>');

-- Sample Bénéficiaires
INSERT INTO beneficiaire (ben_mat, ben_cle, ben_iducnss, ben_nom, ben_prenom, ben_nom_ar, ben_prn_ar, ben_email, ben_tel, ben_numid, ben_typid, ben_dtnais, ben_sexe, ben_type) VALUES
(33333333, 33, 'CNSS33333333', 'BEN ALI', 'MOHAMED', 'بن علي', 'محمد', '<EMAIL>', '98123456', '12345678', 0, DATE '1985-05-15', 1, 1);

INSERT INTO beneficiaire (ben_mat, ben_cle, ben_iducnss, ben_nom, ben_prenom, ben_nom_ar, ben_prn_ar, ben_email, ben_tel, ben_numid, ben_typid, ben_dtnais, ben_sexe, ben_type) VALUES
(44444444, 44, 'CNSS44444444', 'TRABELSI', 'FATMA', 'الطرابلسي', 'فاطمة', '<EMAIL>', '97987654', '87654321', 0, DATE '1990-08-22', 2, 1);

INSERT INTO beneficiaire (ben_mat, ben_cle, ben_iducnss, ben_nom, ben_prenom, ben_nom_ar, ben_prn_ar, ben_email, ben_tel, ben_numid, ben_typid, ben_dtnais, ben_sexe, ben_type) VALUES
(55555555, 55, 'CNSS55555555', 'GHARBI', 'AHMED', 'الغربي', 'أحمد', '<EMAIL>', '96111222', '11223344', 0, DATE '1988-12-03', 1, 2);

INSERT INTO beneficiaire (ben_mat, ben_cle, ben_iducnss, ben_nom, ben_prenom, ben_nom_ar, ben_prn_ar, ben_email, ben_tel, ben_numid, ben_typid, ben_dtnais, ben_sexe, ben_type) VALUES
(66666666, 66, 'CNSS66666666', 'SASSI', 'LEILA', 'الساسي', 'ليلى', '<EMAIL>', '95333444', '55667788', 0, DATE '1992-03-18', 2, 1);

-- Commit all changes
COMMIT;

-- ========================================
-- Verify Table Creation
-- ========================================
SELECT 'EMPLOYEUR' as TABLE_NAME, COUNT(*) as RECORD_COUNT FROM employeur
UNION ALL
SELECT 'ASSURE' as TABLE_NAME, COUNT(*) as RECORD_COUNT FROM assure
UNION ALL
SELECT 'BENEFICIAIRE' as TABLE_NAME, COUNT(*) as RECORD_COUNT FROM beneficiaire;

PROMPT
PROMPT ========================================
PROMPT ✅ CNSS Tables Created Successfully!
PROMPT ========================================
PROMPT
PROMPT Tables created:
PROMPT - EMPLOYEUR (with sample data)
PROMPT - ASSURE (with sample data)  
PROMPT - BENEFICIAIRE (with sample data)
PROMPT
PROMPT You can now start your FastAPI backend!
PROMPT ========================================
