-- =====================================================
-- CNSS Platform - Oracle Database Setup
-- Step 4: Insert Sample Data
-- =====================================================

-- Connect as CNSS_USER before running this script
-- CONNECT cnss_user/CnssPassword123!@localhost:1521/XEPDB1;

-- =====================================================
-- Insert Sample EMPLOYEUR Data
-- =====================================================

INSERT INTO employeur (raison_sociale, numero_cnss, adresse, ville, code_postal, telephone, email, secteur_activite, nombre_employes, created_by)
VALUES ('SOCIETE TUNISIENNE DE BANQUE', 'EMP001', '2 Rue de la Liberté', 'Tunis', '1000', '+216 71 123 456', '<EMAIL>', 'Banque', 150, 'admin');

INSERT INTO employeur (raison_sociale, numero_cnss, adresse, ville, code_postal, telephone, email, secteur_activite, nombre_employes, created_by)
VALUES ('TUNISIE TELECOM', 'EMP002', 'Avenue Habib Bourguiba', 'Tunis', '1001', '+216 71 789 012', '<EMAIL>', 'Télécommunications', 500, 'admin');

INSERT INTO employeur (raison_sociale, numero_cnss, adresse, ville, code_postal, telephone, email, secteur_activite, nombre_employes, created_by)
VALUES ('HOPITAL CHARLES NICOLLE', 'EMP003', 'Boulevard 9 Avril', 'Tunis', '1006', '+216 71 663 000', '<EMAIL>', 'Santé', 800, 'admin');

INSERT INTO employeur (raison_sociale, numero_cnss, adresse, ville, code_postal, telephone, email, secteur_activite, nombre_employes, created_by)
VALUES ('UNIVERSITE DE TUNIS', 'EMP004', 'Campus Universitaire', 'Tunis', '1068', '+216 71 872 600', '<EMAIL>', 'Education', 1200, 'admin');

INSERT INTO employeur (raison_sociale, numero_cnss, adresse, ville, code_postal, telephone, email, secteur_activite, nombre_employes, created_by)
VALUES ('SOCIETE NATIONALE DES CHEMINS DE FER', 'EMP005', 'Place Barcelone', 'Tunis', '1000', '+216 71 244 700', '<EMAIL>', 'Transport', 300, 'admin');

INSERT INTO employeur (raison_sociale, numero_cnss, adresse, ville, code_postal, telephone, email, secteur_activite, nombre_employes, created_by)
VALUES ('OFFICE NATIONAL DU TOURISME', 'EMP006', 'Avenue Mohamed V', 'Tunis', '1001', '+216 71 341 077', '<EMAIL>', 'Tourisme', 80, 'admin');

INSERT INTO employeur (raison_sociale, numero_cnss, adresse, ville, code_postal, telephone, email, secteur_activite, nombre_employes, created_by)
VALUES ('SOCIETE TUNISIENNE D''ELECTRICITE ET DE GAZ', 'EMP007', 'Rue Kamel Ataturk', 'Tunis', '1080', '+216 71 341 411', '<EMAIL>', 'Energie', 600, 'admin');

INSERT INTO employeur (raison_sociale, numero_cnss, adresse, ville, code_postal, telephone, email, secteur_activite, nombre_employes, created_by)
VALUES ('BANQUE CENTRALE DE TUNISIE', 'EMP008', 'Avenue Jean Jaurès', 'Tunis', '1002', '+216 71 340 588', '<EMAIL>', 'Banque Centrale', 250, 'admin');

-- =====================================================
-- Insert Sample ASSURE Data
-- =====================================================

INSERT INTO assure (numero_assure, nom, prenom, date_naissance, lieu_naissance, sexe, cin, adresse, ville, telephone, email, id_employeur, date_embauche, salaire_base, rib, created_by)
VALUES ('ASS001', 'BEN AHMED', 'Mohamed', DATE '1985-03-15', 'Tunis', 'M', '12345678', '10 Rue de la Paix', 'Tunis', '+216 98 123 456', '<EMAIL>', 1, DATE '2020-01-15', 1200.000, '12345678901234567890123456', 'admin');

INSERT INTO assure (numero_assure, nom, prenom, date_naissance, lieu_naissance, sexe, cin, adresse, ville, telephone, email, id_employeur, date_embauche, salaire_base, rib, created_by)
VALUES ('ASS002', 'TRABELSI', 'Fatma', DATE '1990-07-22', 'Sfax', 'F', '87654321', '25 Avenue Bourguiba', 'Sfax', '+216 97 654 321', '<EMAIL>', 2, DATE '2021-06-01', 950.000, '65432109876543210987654321', 'admin');

INSERT INTO assure (numero_assure, nom, prenom, date_naissance, lieu_naissance, sexe, cin, adresse, ville, telephone, email, id_employeur, date_embauche, salaire_base, rib, created_by)
VALUES ('ASS003', 'KHELIFI', 'Ahmed', DATE '1982-11-08', 'Sousse', 'M', '11223344', '5 Rue des Martyrs', 'Sousse', '+216 96 112 233', '<EMAIL>', 3, DATE '2019-03-10', 1500.000, '11223344556677889900112233', 'admin');

INSERT INTO assure (numero_assure, nom, prenom, date_naissance, lieu_naissance, sexe, cin, adresse, ville, telephone, email, id_employeur, date_embauche, salaire_base, rib, created_by)
VALUES ('ASS004', 'MANSOURI', 'Leila', DATE '1988-09-12', 'Monastir', 'F', '55667788', '15 Rue de la République', 'Monastir', '+216 95 556 677', '<EMAIL>', 4, DATE '2022-02-01', 1100.000, '55667788990011223344556677', 'admin');

INSERT INTO assure (numero_assure, nom, prenom, date_naissance, lieu_naissance, sexe, cin, adresse, ville, telephone, email, id_employeur, date_embauche, salaire_base, rib, created_by)
VALUES ('ASS005', 'GHARBI', 'Karim', DATE '1983-04-25', 'Bizerte', 'M', '99887766', '8 Avenue de la Liberté', 'Bizerte', '+216 94 998 877', '<EMAIL>', 5, DATE '2018-09-15', 1350.000, '99887766554433221100998877', 'admin');

INSERT INTO assure (numero_assure, nom, prenom, date_naissance, lieu_naissance, sexe, cin, adresse, ville, telephone, email, id_employeur, date_embauche, salaire_base, rib, created_by)
VALUES ('ASS006', 'BOUAZIZI', 'Sonia', DATE '1992-01-30', 'Kairouan', 'F', '33445566', '12 Rue des Fleurs', 'Kairouan', '+216 93 334 455', '<EMAIL>', 6, DATE '2023-01-10', 850.000, '33445566778899001122334455', 'admin');

INSERT INTO assure (numero_assure, nom, prenom, date_naissance, lieu_naissance, sexe, cin, adresse, ville, telephone, email, id_employeur, date_embauche, salaire_base, rib, created_by)
VALUES ('ASS007', 'JEMLI', 'Hichem', DATE '1980-12-05', 'Gafsa', 'M', '77889900', '20 Boulevard Habib Bourguiba', 'Gafsa', '+216 92 778 899', '<EMAIL>', 7, DATE '2017-05-20', 1600.000, '77889900112233445566778899', 'admin');

INSERT INTO assure (numero_assure, nom, prenom, date_naissance, lieu_naissance, sexe, cin, adresse, ville, telephone, email, id_employeur, date_embauche, salaire_base, rib, created_by)
VALUES ('ASS008', 'NASRI', 'Amina', DATE '1987-06-18', 'Mahdia', 'F', '11335577', '7 Rue de la Paix', 'Mahdia', '+216 91 113 355', '<EMAIL>', 8, DATE '2021-11-01', 1250.000, '11335577992244668800113355', 'admin');

-- =====================================================
-- Insert Sample BENEFICIAIRE Data
-- =====================================================

-- Beneficiaires for Mohamed BEN AHMED (ASS001)
INSERT INTO beneficiaire (numero_beneficiaire, id_assure, nom, prenom, date_naissance, lieu_naissance, sexe, relation, cin, adresse, ville, telephone, created_by)
VALUES ('BEN001', 1, 'BEN AHMED', 'Aisha', DATE '1987-05-20', 'Tunis', 'F', 'CONJOINT', '12345679', '10 Rue de la Paix', 'Tunis', '+216 98 123 457', 'admin');

INSERT INTO beneficiaire (numero_beneficiaire, id_assure, nom, prenom, date_naissance, lieu_naissance, sexe, relation, adresse, ville, created_by)
VALUES ('BEN002', 1, 'BEN AHMED', 'Youssef', DATE '2015-12-10', 'Tunis', 'M', 'ENFANT', '10 Rue de la Paix', 'Tunis', 'admin');

INSERT INTO beneficiaire (numero_beneficiaire, id_assure, nom, prenom, date_naissance, lieu_naissance, sexe, relation, adresse, ville, created_by)
VALUES ('BEN003', 1, 'BEN AHMED', 'Yasmine', DATE '2018-08-22', 'Tunis', 'F', 'ENFANT', '10 Rue de la Paix', 'Tunis', 'admin');

-- Beneficiaires for Fatma TRABELSI (ASS002)
INSERT INTO beneficiaire (numero_beneficiaire, id_assure, nom, prenom, date_naissance, lieu_naissance, sexe, relation, cin, adresse, ville, telephone, created_by)
VALUES ('BEN004', 2, 'TRABELSI', 'Salma', DATE '2020-03-15', 'Sfax', 'F', 'ENFANT', NULL, '25 Avenue Bourguiba', 'Sfax', NULL, 'admin');

-- Beneficiaires for Ahmed KHELIFI (ASS003)
INSERT INTO beneficiaire (numero_beneficiaire, id_assure, nom, prenom, date_naissance, lieu_naissance, sexe, relation, cin, adresse, ville, telephone, created_by)
VALUES ('BEN005', 3, 'KHELIFI', 'Maryam', DATE '1985-02-14', 'Sousse', 'F', 'CONJOINT', '11223345', '5 Rue des Martyrs', 'Sousse', '+216 96 112 234', 'admin');

INSERT INTO beneficiaire (numero_beneficiaire, id_assure, nom, prenom, date_naissance, lieu_naissance, sexe, relation, adresse, ville, created_by)
VALUES ('BEN006', 3, 'KHELIFI', 'Omar', DATE '2012-07-08', 'Sousse', 'M', 'ENFANT', '5 Rue des Martyrs', 'Sousse', 'admin');

INSERT INTO beneficiaire (numero_beneficiaire, id_assure, nom, prenom, date_naissance, lieu_naissance, sexe, relation, adresse, ville, created_by)
VALUES ('BEN007', 3, 'KHELIFI', 'Nour', DATE '2016-11-25', 'Sousse', 'F', 'ENFANT', '5 Rue des Martyrs', 'Sousse', 'admin');

-- Beneficiaires for Leila MANSOURI (ASS004)
INSERT INTO beneficiaire (numero_beneficiaire, id_assure, nom, prenom, date_naissance, lieu_naissance, sexe, relation, cin, adresse, ville, telephone, created_by)
VALUES ('BEN008', 4, 'MANSOURI', 'Rami', DATE '2019-04-12', 'Monastir', 'M', 'ENFANT', NULL, '15 Rue de la République', 'Monastir', NULL, 'admin');

-- Beneficiaires for Karim GHARBI (ASS005)
INSERT INTO beneficiaire (numero_beneficiaire, id_assure, nom, prenom, date_naissance, lieu_naissance, sexe, relation, cin, adresse, ville, telephone, created_by)
VALUES ('BEN009', 5, 'GHARBI', 'Samira', DATE '1986-08-30', 'Bizerte', 'F', 'CONJOINT', '99887767', '8 Avenue de la Liberté', 'Bizerte', '+216 94 998 878', 'admin');

INSERT INTO beneficiaire (numero_beneficiaire, id_assure, nom, prenom, date_naissance, lieu_naissance, sexe, relation, adresse, ville, created_by)
VALUES ('BEN010', 5, 'GHARBI', 'Amine', DATE '2014-01-15', 'Bizerte', 'M', 'ENFANT', '8 Avenue de la Liberté', 'Bizerte', 'admin');

-- Beneficiaires for Hichem JEMLI (ASS007)
INSERT INTO beneficiaire (numero_beneficiaire, id_assure, nom, prenom, date_naissance, lieu_naissance, sexe, relation, cin, adresse, ville, telephone, created_by)
VALUES ('BEN011', 7, 'JEMLI', 'Khadija', DATE '1982-09-10', 'Gafsa', 'F', 'CONJOINT', '77889901', '20 Boulevard Habib Bourguiba', 'Gafsa', '+216 92 778 900', 'admin');

INSERT INTO beneficiaire (numero_beneficiaire, id_assure, nom, prenom, date_naissance, lieu_naissance, sexe, relation, adresse, ville, created_by)
VALUES ('BEN012', 7, 'JEMLI', 'Sami', DATE '2010-05-22', 'Gafsa', 'M', 'ENFANT', '20 Boulevard Habib Bourguiba', 'Gafsa', 'admin');

INSERT INTO beneficiaire (numero_beneficiaire, id_assure, nom, prenom, date_naissance, lieu_naissance, sexe, relation, adresse, ville, created_by)
VALUES ('BEN013', 7, 'JEMLI', 'Lina', DATE '2013-12-03', 'Gafsa', 'F', 'ENFANT', '20 Boulevard Habib Bourguiba', 'Gafsa', 'admin');

-- Commit all data
COMMIT;

-- =====================================================
-- Verify Data Insertion
-- =====================================================

-- Count records in each table
SELECT 'EMPLOYEUR' as table_name, COUNT(*) as record_count FROM employeur
UNION ALL
SELECT 'ASSURE' as table_name, COUNT(*) as record_count FROM assure
UNION ALL
SELECT 'BENEFICIAIRE' as table_name, COUNT(*) as record_count FROM beneficiaire;

-- Show sample data with relationships
SELECT 
    e.raison_sociale as employeur,
    a.nom || ' ' || a.prenom as assure_nom,
    COUNT(b.id_beneficiaire) as nb_beneficiaires
FROM employeur e
LEFT JOIN assure a ON e.id_employeur = a.id_employeur
LEFT JOIN beneficiaire b ON a.id_assure = b.id_assure
GROUP BY e.raison_sociale, a.nom, a.prenom
ORDER BY e.raison_sociale, a.nom;

PROMPT 'Sample data inserted successfully!'
PROMPT 'Next: Run 05_create_views_and_procedures.sql for additional database objects'
