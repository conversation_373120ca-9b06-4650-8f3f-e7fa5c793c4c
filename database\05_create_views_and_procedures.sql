-- =====================================================
-- CNSS Platform - Oracle Database Setup
-- Step 5: Create Views and Stored Procedures
-- =====================================================

-- Connect as CNSS_USER before running this script
-- CONNECT cnss_user/CnssPassword123!@localhost:1521/XEPDB1;

-- =====================================================
-- Create Views for Common Queries
-- =====================================================

-- View: Complete employeur information with statistics
CREATE OR REPLACE VIEW v_employeur_stats AS
SELECT 
    e.id_employeur,
    e.raison_sociale,
    e.numero_cnss,
    e.adresse,
    e.ville,
    e.code_postal,
    e.telephone,
    e.email,
    e.secteur_activite,
    e.nombre_employes,
    e.statut,
    e.date_creation,
    COUNT(a.id_assure) as nb_assures_actifs,
    AVG(a.salaire_base) as salaire_moyen,
    MIN(a.date_embauche) as premiere_embauche,
    MAX(a.date_embauche) as derniere_embauche
FROM employeur e
LEFT JOIN assure a ON e.id_employeur = a.id_employeur AND a.statut = 'ACTIF'
GROUP BY e.id_employeur, e.raison_sociale, e.numero_cnss, e.adresse, e.ville, 
         e.code_postal, e.telephone, e.email, e.secteur_activite, e.nombre_employes, 
         e.statut, e.date_creation;

-- View: Complete assure information with employeur and beneficiaires
CREATE OR REPLACE VIEW v_assure_complet AS
SELECT 
    a.id_assure,
    a.numero_assure,
    a.nom,
    a.prenom,
    a.date_naissance,
    TRUNC((SYSDATE - a.date_naissance) / 365.25) as age,
    a.lieu_naissance,
    a.sexe,
    a.nationalite,
    a.cin,
    a.adresse,
    a.ville,
    a.telephone,
    a.email,
    a.date_embauche,
    a.salaire_base,
    a.rib,
    a.statut,
    e.raison_sociale as employeur_nom,
    e.numero_cnss as employeur_numero,
    e.secteur_activite,
    COUNT(b.id_beneficiaire) as nb_beneficiaires
FROM assure a
LEFT JOIN employeur e ON a.id_employeur = e.id_employeur
LEFT JOIN beneficiaire b ON a.id_assure = b.id_assure AND b.statut = 'ACTIF'
GROUP BY a.id_assure, a.numero_assure, a.nom, a.prenom, a.date_naissance, 
         a.lieu_naissance, a.sexe, a.nationalite, a.cin, a.adresse, a.ville, 
         a.telephone, a.email, a.date_embauche, a.salaire_base, a.rib, a.statut,
         e.raison_sociale, e.numero_cnss, e.secteur_activite;

-- View: Complete beneficiaire information with assure details
CREATE OR REPLACE VIEW v_beneficiaire_complet AS
SELECT 
    b.id_beneficiaire,
    b.numero_beneficiaire,
    b.nom,
    b.prenom,
    b.date_naissance,
    TRUNC((SYSDATE - b.date_naissance) / 365.25) as age,
    b.lieu_naissance,
    b.sexe,
    b.relation,
    b.cin,
    b.adresse,
    b.ville,
    b.telephone,
    b.email,
    b.date_debut_couverture,
    b.date_fin_couverture,
    b.statut,
    a.numero_assure,
    a.nom || ' ' || a.prenom as assure_nom,
    a.telephone as assure_telephone,
    e.raison_sociale as employeur_nom
FROM beneficiaire b
JOIN assure a ON b.id_assure = a.id_assure
LEFT JOIN employeur e ON a.id_employeur = e.id_employeur;

-- View: Dashboard statistics
CREATE OR REPLACE VIEW v_dashboard_stats AS
SELECT 
    (SELECT COUNT(*) FROM employeur WHERE statut = 'ACTIF') as total_employeurs,
    (SELECT COUNT(*) FROM employeur WHERE statut = 'ACTIF') as employeurs_actifs,
    (SELECT COUNT(*) FROM employeur WHERE statut = 'INACTIF') as employeurs_inactifs,
    (SELECT COUNT(*) FROM assure WHERE statut = 'ACTIF') as total_assures,
    (SELECT COUNT(*) FROM assure WHERE statut = 'ACTIF' AND id_employeur IS NOT NULL) as assures_avec_employeur,
    (SELECT COUNT(*) FROM assure WHERE statut = 'ACTIF' AND rib IS NOT NULL) as assures_avec_rib,
    (SELECT COUNT(*) FROM beneficiaire WHERE statut = 'ACTIF') as total_beneficiaires,
    (SELECT COUNT(*) FROM beneficiaire WHERE statut = 'ACTIF' AND sexe = 'M') as beneficiaires_hommes,
    (SELECT COUNT(*) FROM beneficiaire WHERE statut = 'ACTIF' AND sexe = 'F') as beneficiaires_femmes
FROM dual;

-- =====================================================
-- Create Stored Procedures
-- =====================================================

-- Procedure: Get employeur statistics
CREATE OR REPLACE PROCEDURE sp_get_employeur_stats(
    p_employeur_id IN NUMBER DEFAULT NULL,
    p_cursor OUT SYS_REFCURSOR
) AS
BEGIN
    IF p_employeur_id IS NULL THEN
        OPEN p_cursor FOR
        SELECT * FROM v_employeur_stats
        ORDER BY raison_sociale;
    ELSE
        OPEN p_cursor FOR
        SELECT * FROM v_employeur_stats
        WHERE id_employeur = p_employeur_id;
    END IF;
END sp_get_employeur_stats;
/

-- Procedure: Get assure statistics
CREATE OR REPLACE PROCEDURE sp_get_assure_stats(
    p_assure_id IN NUMBER DEFAULT NULL,
    p_employeur_id IN NUMBER DEFAULT NULL,
    p_cursor OUT SYS_REFCURSOR
) AS
BEGIN
    IF p_assure_id IS NOT NULL THEN
        OPEN p_cursor FOR
        SELECT * FROM v_assure_complet
        WHERE id_assure = p_assure_id;
    ELSIF p_employeur_id IS NOT NULL THEN
        OPEN p_cursor FOR
        SELECT * FROM v_assure_complet
        WHERE id_assure IN (SELECT id_assure FROM assure WHERE id_employeur = p_employeur_id)
        ORDER BY nom, prenom;
    ELSE
        OPEN p_cursor FOR
        SELECT * FROM v_assure_complet
        ORDER BY nom, prenom;
    END IF;
END sp_get_assure_stats;
/

-- Procedure: Get beneficiaire statistics
CREATE OR REPLACE PROCEDURE sp_get_beneficiaire_stats(
    p_beneficiaire_id IN NUMBER DEFAULT NULL,
    p_assure_id IN NUMBER DEFAULT NULL,
    p_cursor OUT SYS_REFCURSOR
) AS
BEGIN
    IF p_beneficiaire_id IS NOT NULL THEN
        OPEN p_cursor FOR
        SELECT * FROM v_beneficiaire_complet
        WHERE id_beneficiaire = p_beneficiaire_id;
    ELSIF p_assure_id IS NOT NULL THEN
        OPEN p_cursor FOR
        SELECT * FROM v_beneficiaire_complet
        WHERE id_assure = p_assure_id
        ORDER BY relation, nom, prenom;
    ELSE
        OPEN p_cursor FOR
        SELECT * FROM v_beneficiaire_complet
        ORDER BY assure_nom, relation, nom, prenom;
    END IF;
END sp_get_beneficiaire_stats;
/

-- Procedure: Search across all entities
CREATE OR REPLACE PROCEDURE sp_search_global(
    p_search_term IN VARCHAR2,
    p_cursor OUT SYS_REFCURSOR
) AS
    v_search_term VARCHAR2(200);
BEGIN
    v_search_term := '%' || UPPER(p_search_term) || '%';
    
    OPEN p_cursor FOR
    SELECT 'EMPLOYEUR' as entity_type, 
           id_employeur as entity_id,
           raison_sociale as name,
           numero_cnss as identifier,
           ville,
           telephone,
           email
    FROM employeur 
    WHERE UPPER(raison_sociale) LIKE v_search_term
       OR UPPER(numero_cnss) LIKE v_search_term
       OR UPPER(email) LIKE v_search_term
    UNION ALL
    SELECT 'ASSURE' as entity_type,
           id_assure as entity_id,
           nom || ' ' || prenom as name,
           numero_assure as identifier,
           ville,
           telephone,
           email
    FROM assure
    WHERE UPPER(nom) LIKE v_search_term
       OR UPPER(prenom) LIKE v_search_term
       OR UPPER(numero_assure) LIKE v_search_term
       OR UPPER(cin) LIKE v_search_term
       OR UPPER(email) LIKE v_search_term
    UNION ALL
    SELECT 'BENEFICIAIRE' as entity_type,
           id_beneficiaire as entity_id,
           nom || ' ' || prenom as name,
           numero_beneficiaire as identifier,
           ville,
           telephone,
           email
    FROM beneficiaire
    WHERE UPPER(nom) LIKE v_search_term
       OR UPPER(prenom) LIKE v_search_term
       OR UPPER(numero_beneficiaire) LIKE v_search_term
       OR UPPER(cin) LIKE v_search_term
       OR UPPER(email) LIKE v_search_term
    ORDER BY entity_type, name;
END sp_search_global;
/

-- =====================================================
-- Create Functions for Business Logic
-- =====================================================

-- Function: Calculate age from birth date
CREATE OR REPLACE FUNCTION fn_calculate_age(p_birth_date IN DATE)
RETURN NUMBER
IS
BEGIN
    RETURN TRUNC((SYSDATE - p_birth_date) / 365.25);
END fn_calculate_age;
/

-- Function: Generate next numero for entities
CREATE OR REPLACE FUNCTION fn_generate_numero(p_entity_type IN VARCHAR2)
RETURN VARCHAR2
IS
    v_next_number NUMBER;
    v_prefix VARCHAR2(10);
BEGIN
    CASE UPPER(p_entity_type)
        WHEN 'EMPLOYEUR' THEN
            SELECT NVL(MAX(TO_NUMBER(SUBSTR(numero_cnss, 4))), 0) + 1 
            INTO v_next_number 
            FROM employeur 
            WHERE numero_cnss LIKE 'EMP%';
            v_prefix := 'EMP';
        WHEN 'ASSURE' THEN
            SELECT NVL(MAX(TO_NUMBER(SUBSTR(numero_assure, 4))), 0) + 1 
            INTO v_next_number 
            FROM assure 
            WHERE numero_assure LIKE 'ASS%';
            v_prefix := 'ASS';
        WHEN 'BENEFICIAIRE' THEN
            SELECT NVL(MAX(TO_NUMBER(SUBSTR(numero_beneficiaire, 4))), 0) + 1 
            INTO v_next_number 
            FROM beneficiaire 
            WHERE numero_beneficiaire LIKE 'BEN%';
            v_prefix := 'BEN';
        ELSE
            RETURN NULL;
    END CASE;
    
    RETURN v_prefix || LPAD(v_next_number, 3, '0');
END fn_generate_numero;
/

-- =====================================================
-- Grant Permissions on Views and Procedures
-- =====================================================

-- Grant select on views (if needed for other users)
-- GRANT SELECT ON v_employeur_stats TO other_user;
-- GRANT SELECT ON v_assure_complet TO other_user;
-- GRANT SELECT ON v_beneficiaire_complet TO other_user;
-- GRANT SELECT ON v_dashboard_stats TO other_user;

-- =====================================================
-- Verify Creation
-- =====================================================

-- Show created views
SELECT view_name FROM user_views ORDER BY view_name;

-- Show created procedures and functions
SELECT object_name, object_type, status 
FROM user_objects 
WHERE object_type IN ('PROCEDURE', 'FUNCTION')
ORDER BY object_type, object_name;

-- Test dashboard stats view
SELECT * FROM v_dashboard_stats;

PROMPT 'Views, procedures, and functions created successfully!'
PROMPT 'Database setup is now complete!'
PROMPT 'You can now connect your FastAPI application to the database.'
