-- Create CNSS database user and schema
-- This script should be run as SYSTEM user

-- Create tablespace for CNSS data
CREATE TABLESPACE cnss_data
DATAFILE '/opt/oracle/oradata/XE/cnss_data01.dbf' SIZE 100M
AUTOEXTEND ON NEXT 10M MAXSIZE 1G;

-- Create tablespace for CNSS indexes
CREATE TABLESPACE cnss_index
DATAFILE '/opt/oracle/oradata/XE/cnss_index01.dbf' SIZE 50M
AUTOEXTEND ON NEXT 5M MAXSIZE 500M;

-- Create CNSS user
CREATE USER cnss_user IDENTIFIED BY cnss_password
DEFAULT TABLESPACE cnss_data
TEMPORARY TABLESPACE temp
QUOTA UNLIMITED ON cnss_data
QUOTA UNLIMITED ON cnss_index;

-- Grant necessary privileges
GRANT CONNECT, RESOURCE TO cnss_user;
GRANT CREATE SESSION TO cnss_user;
GRANT CREATE TABLE TO cnss_user;
GRANT CREATE SEQUENCE TO cnss_user;
GRANT CREATE VIEW TO cnss_user;
GRANT CREATE PROCEDURE TO cnss_user;
GRANT CREATE TRIGGER TO cnss_user;

-- Grant additional privileges for application
GRANT SELECT_CATALOG_ROLE TO cnss_user;
GRANT EXECUTE ON DBMS_STATS TO cnss_user;

-- Create directory for file operations (if needed)
CREATE OR REPLACE DIRECTORY cnss_dir AS '/opt/oracle/cnss_files';
GRANT READ, WRITE ON DIRECTORY cnss_dir TO cnss_user;

COMMIT;
