-- Seed data for CNSS platform
-- This script creates initial data for testing and development

-- Connect as cnss_user
CONNECT cnss_user/cnss_password@XE;

-- Insert default admin user
-- Password: admin123 (hashed with bcrypt)
INSERT INTO users (
    id, email, first_name, last_name, hashed_password, role, is_active, is_verified, created_at
) VALUES (
    1, 
    '<EMAIL>', 
    'Admin', 
    'CNSS', 
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBdXzgVjHUxrLW', -- admin123
    'admin', 
    1, 
    1, 
    CURRENT_TIMESTAMP
);

-- Insert test agent user
-- Password: agent123 (hashed with bcrypt)
INSERT INTO users (
    id, email, first_name, last_name, hashed_password, role, is_active, is_verified, created_at
) VALUES (
    2, 
    '<EMAIL>', 
    'Agent', 
    'Test', 
    '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPDlvfLqG', -- agent123
    'agent', 
    1, 
    1, 
    CURRENT_TIMESTAMP
);

-- Insert sample employers
INSERT INTO employers (
    id, company_name, registration_number, tax_id, cnss_number,
    email, phone, address_line1, city, postal_code, region,
    legal_rep_name, legal_rep_title, is_active, is_verified, created_at
) VALUES (
    1, 
    'TechCorp SARL', 
    'RC123456789', 
    'TAX987654321', 
    'CNSS001234567',
    '<EMAIL>', 
    '+216 71 123 456', 
    '123 Avenue Habib Bourguiba', 
    'Tunis', 
    '1000', 
    'Tunis',
    'Ahmed Ben Ali', 
    'Directeur Général', 
    1, 
    1, 
    CURRENT_TIMESTAMP
);

INSERT INTO employers (
    id, company_name, registration_number, tax_id, cnss_number,
    email, phone, address_line1, city, postal_code, region,
    legal_rep_name, legal_rep_title, is_active, is_verified, created_at
) VALUES (
    2, 
    'InnovateTech SA', 
    'RC987654321', 
    'TAX123456789', 
    'CNSS007654321',
    '<EMAIL>', 
    '+216 71 987 654', 
    '456 Rue de la République', 
    'Sfax', 
    '3000', 
    'Sfax',
    'Fatma Trabelsi', 
    'PDG', 
    1, 
    1, 
    CURRENT_TIMESTAMP
);

-- Insert sample services
INSERT INTO services (
    id, name, code, description, service_type, status,
    endpoint_url, http_method, requires_auth, version, created_at, created_by
) VALUES (
    1, 
    'Déclaration Mensuelle', 
    'DECL_MONTHLY', 
    'Service de déclaration mensuelle des salaires et cotisations',
    'declaration', 
    'active',
    '/api/declarations/monthly', 
    'POST', 
    1, 
    '1.0', 
    CURRENT_TIMESTAMP, 
    1
);

INSERT INTO services (
    id, name, code, description, service_type, status,
    endpoint_url, http_method, requires_auth, version, created_at, created_by
) VALUES (
    2, 
    'Paiement Cotisations', 
    'PAY_CONTRIB', 
    'Service de paiement des cotisations sociales',
    'payment', 
    'active',
    '/api/payments/contributions', 
    'POST', 
    1, 
    '1.0', 
    CURRENT_TIMESTAMP, 
    1
);

INSERT INTO services (
    id, name, code, description, service_type, status,
    endpoint_url, http_method, requires_auth, version, created_at, created_by
) VALUES (
    3, 
    'Consultation Dossier', 
    'INQ_DOSSIER', 
    'Service de consultation du dossier employeur',
    'inquiry', 
    'active',
    '/api/inquiries/dossier', 
    'GET', 
    1, 
    '1.0', 
    CURRENT_TIMESTAMP, 
    1
);

-- Create sequences for auto-increment IDs
CREATE SEQUENCE users_seq START WITH 3 INCREMENT BY 1;
CREATE SEQUENCE employers_seq START WITH 3 INCREMENT BY 1;
CREATE SEQUENCE services_seq START WITH 4 INCREMENT BY 1;

COMMIT;
