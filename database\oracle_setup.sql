-- Oracle Database Setup Script for CNSS Platform
-- This script creates the three main tables: employeur, assure, beneficiaire
-- Run this script in your Oracle 23c database

-- =====================================================
-- EMPLOYEUR TABLE
-- =====================================================
CREATE TABLE employeur (
    EMP_MAT             NUMBER(8) NOT NULL,
    EMP_CLE             NUMBER(2) NOT NULL,
    ADM_COD             VARCHAR2(4),
    PAT_NUM             NUMBER(7),
    PAT_CLE             CHAR(1),
    ATV_COD             NUMBER(6),
    RPL_ID              VARCHAR2(15),
    LOC_CODPOS          NUMBER(4),
    LOC_INDICE          NUMBER(1),
    BUR_COD             NUMBER(3),
    ATM_REF             NUMBER(4),
    DCT_DTCOT           NUMBER(1),
    LOC_LOC_CODPOS      NUMBER(4) NOT NULL,
    LOC_LOC_INDICE      NUMBER(1) NOT NULL,
    REG_COD             NUMBER(3) NOT NULL,
    PAY_COD             NUMBER(4) NOT NULL,
    EMP_DTEFF           DATE,
    EMP_DTASSUJ         DATE,
    EMP_RAIS            VARCHAR2(80),
    EMP_RAIS_AR         VARCHAR2(80),
    EMP_TEL             VARCHAR2(10),
    EMP_FAX             VARCHAR2(10),
    EMP_EMAIL           VARCHAR2(60),
    EMP_SIGLE           VARCHAR2(15),
    EMP_ENSEIG          VARCHAR2(10),
    EMP_DTDACT          DATE,
    EMP_NREGC           VARCHAR2(12),
    EMP_ESTINT          NUMBER(15,3),
    EMP_CATPROF         VARCHAR2(1),
    EMP_EXAFFMAT        NUMBER(8),
    EMP_EXAFFCLE        NUMBER(2),
    EMP_RIB             NUMBER(20),
    EMP_TYPAFFI         NUMBER(1),
    EMP_ADRCOR          VARCHAR2(100),
    EMP_ADRCOR_AR       VARCHAR2(100),
    EMP_DERADR          VARCHAR2(100),
    EMP_DERAR_AR        VARCHAR2(100),
    EMP_DTREGCP         DATE,
    EMP_NUMADH          NUMBER(4),
    EMP_CREGCMP         CHAR(1),
    EMP_INCONNU         NUMBER(1),
    EMP_FLAGPER         NUMBER(1),
    EMP_LREGC           VARCHAR2(30),
    EMP_CAPRES          NUMBER(2),
    EMP_DTAFF           DATE,
    EMP_ACTIVITE        VARCHAR2(60),
    EMP_DTMAJ           DATE,
    EMP_MATAG           NUMBER(6),
    EMP_DTFINREGCP      DATE,
    EMP_IDENTREPADHREGC VARCHAR2(100),
    EMP_QUALREPADHREGC  VARCHAR2(100),
    CONSTRAINT PK_EMPLOYEUR PRIMARY KEY (EMP_MAT, EMP_CLE)
);

-- =====================================================
-- ASSURE TABLE
-- =====================================================
CREATE TABLE assure (
    ASS_MAT         NUMBER(8) NOT NULL,
    ASS_CLE         NUMBER(2) NOT NULL,
    BUR_COD         NUMBER(3),
    PAY_COD         NUMBER(4) NOT NULL,
    EMP_MAT         NUMBER(8),
    EMP_CLE         NUMBER(2),
    ASS_IU          NUMBER(10) NOT NULL,
    ASS_DTEFF       DATE,
    ASS_DTIMMAT     DATE,
    ASS_DTSIM       DATE,
    ASS_CODAG       NUMBER(6),
    ASS_DTVALID     DATE,
    ASS_DTTCENT     DATE,
    ASS_SALUNI      NUMBER(1),
    ASS_RIB         NUMBER(20),
    ASS_CNRPS       NUMBER(10),
    ASS_BRCREAT     NUMBER(3),
    ASS_DTASSUJ     DATE,
    ASS_FLPR        NUMBER(1),
    ASS_DERREG      NUMBER(3),
    ASS_AGENT       NUMBER(6),
    ETAT_CHARGEMENT VARCHAR2(100),
    BEN_RC          NUMBER(12),
    METHRECEPDOSSID NUMBER,
    CONSTRAINT PK_ASSURE PRIMARY KEY (ASS_MAT, ASS_CLE)
);

-- =====================================================
-- BENEFICIAIRE TABLE
-- =====================================================
CREATE TABLE beneficiaire (
    BEN_IDUCNSS             NUMBER(8) NOT NULL,
    LOC_CODPOS              NUMBER(4),
    LOC_INDICE              NUMBER(1),
    ASS_MAT                 NUMBER(8) NOT NULL,
    ASS_CLE                 NUMBER(2) NOT NULL,
    PAY_COD                 NUMBER(4) NOT NULL,
    BEN_TYPE                NUMBER(1) NOT NULL,
    BEN_RANG                NUMBER(2) NOT NULL,
    BEN_IU                  NUMBER(10),
    BEN_NOM                 VARCHAR2(30),
    BEN_NOM_AR              VARCHAR2(30),
    BEN_PRENOM              VARCHAR2(30),
    BEN_PRN_AR              VARCHAR2(30),
    BEN_PRNPER              VARCHAR2(30),
    BEN_PRNPER_AR           VARCHAR2(30),
    BEN_PRNGP               VARCHAR2(30),
    BEN_PRNGP_AR            VARCHAR2(30),
    BEN_PRNMER              VARCHAR2(30),
    BEN_PRNMER_AR           VARCHAR2(30),
    BEN_NOMMER              VARCHAR2(30),
    BEN_NOMMER_AR           VARCHAR2(30),
    BEN_SEXE                NUMBER(1),
    BEN_SITFAM              NUMBER(1),
    BEN_DTNAIS              DATE,
    BEN_LNAIS               VARCHAR2(30),
    BEN_LNAIS_AR            VARCHAR2(30),
    BEN_TYPID               NUMBER(1),
    BEN_NUMID               VARCHAR2(15),
    BEN_DTID                DATE,
    BEN_ADR                 VARCHAR2(120),
    BEN_ADR_AR              VARCHAR2(120),
    BEN_CODPIECE            CHAR(1),
    BEN_NACTN               NUMBER(6),
    BEN_AACTN               NUMBER(4),
    BEN_LACTN               VARCHAR2(20),
    BEN_DRAF                NUMBER(1),
    BEN_DTDAF               DATE,
    BEN_DTFAF               DATE,
    BEN_DSOIN               NUMBER(1),
    BEN_DTDSOIN             DATE,
    BEN_DTFSOIN             DATE,
    BEN_FLPER               NUMBER(1),
    BEN_LPAR                NUMBER(1),
    BEN_AGENT               NUMBER(6),
    ETR_MAT                 NUMBER(19),
    BEN_IMADAT              NUMBER(5),
    BEN_CONSULAT            NUMBER(1),
    BEN_ARRONDIS            NUMBER(2),
    BEN_ANNREG              NUMBER(4),
    BEN_NUMACT              NUMBER(6),
    BEN_MUNICIP             NUMBER(5),
    BEN_VALIDACT            NUMBER(1),
    ARRONDISSEMENT_GVT_COD  NUMBER(2),
    ARRONDISSEMENT_DLG_COD  NUMBER(2),
    ARRONDISSEMENT_MUN_CODE NUMBER(4),
    ARRONDISSEMENT_ARO_CODE NUMBER(4),
    BEN_DTSITFAM            DATE,
    PRE_FRA_STD             VARCHAR2(250),
    NOM_FRA_STD             VARCHAR2(250),
    BEN_RC                  NUMBER(12),
    BEN_TEL                 NUMBER(8),
    BEN_EMAIL               VARCHAR2(30),
    BEN_IDENTITEMADANIA     VARCHAR2(500),
    BEN_IDENTITEMEREMADANIA VARCHAR2(500),
    BEN_DTSAISIE            DATE,
    BEN_DTMAJ               DATE,
    CONSTRAINT PK_BENEFICIAIRE PRIMARY KEY (BEN_IDUCNSS)
);

-- =====================================================
-- SAMPLE DATA INSERTION
-- =====================================================

-- Insert sample employeurs
INSERT INTO employeur (EMP_MAT, EMP_CLE, LOC_LOC_CODPOS, LOC_LOC_INDICE, REG_COD, PAY_COD, EMP_RAIS, EMP_EMAIL, EMP_TEL, EMP_ACTIVITE, EMP_DTAFF, EMP_DTASSUJ) VALUES
(12345678, 12, 1001, 1, 101, 216, 'TechCorp SARL', '<EMAIL>', '71234567', 'Développement logiciel', DATE '2020-01-15', DATE '2020-01-15');

INSERT INTO employeur (EMP_MAT, EMP_CLE, LOC_LOC_CODPOS, LOC_LOC_INDICE, REG_COD, PAY_COD, EMP_RAIS, EMP_EMAIL, EMP_TEL, EMP_ACTIVITE, EMP_DTAFF, EMP_DTASSUJ) VALUES
(87654321, 34, 1002, 1, 102, 216, 'InnovateTech SA', '<EMAIL>', '71987654', 'Consulting IT', DATE '2019-03-20', DATE '2019-03-20');

INSERT INTO employeur (EMP_MAT, EMP_CLE, LOC_LOC_CODPOS, LOC_LOC_INDICE, REG_COD, PAY_COD, EMP_RAIS, EMP_EMAIL, EMP_TEL, EMP_ACTIVITE, EMP_DTAFF, EMP_DTASSUJ) VALUES
(11223344, 56, 1003, 1, 103, 216, 'Digital Solutions Ltd', '<EMAIL>', '71112233', 'Solutions digitales', DATE '2021-06-10', DATE '2021-06-10');

-- Insert sample assures
INSERT INTO assure (ASS_MAT, ASS_CLE, PAY_COD, EMP_MAT, EMP_CLE, ASS_IU, ASS_DTEFF, ASS_DTIMMAT, ASS_RIB, ASS_CNRPS) VALUES
(11111111, 11, 216, 12345678, 12, 1001, DATE '2020-02-01', DATE '2020-02-01', 12345678901234567890, 1234567890);

INSERT INTO assure (ASS_MAT, ASS_CLE, PAY_COD, EMP_MAT, EMP_CLE, ASS_IU, ASS_DTEFF, ASS_DTIMMAT, ASS_RIB, ASS_CNRPS) VALUES
(22222222, 22, 216, 87654321, 34, 1002, DATE '2019-04-15', DATE '2019-04-15', 98765432109876543210, 0987654321);

INSERT INTO assure (ASS_MAT, ASS_CLE, PAY_COD, EMP_MAT, EMP_CLE, ASS_IU, ASS_DTEFF, ASS_DTIMMAT, ASS_RIB, ASS_CNRPS) VALUES
(33333333, 33, 216, 11223344, 56, 1003, DATE '2021-07-01', DATE '2021-07-01', 11223344556677889900, 1122334455);

-- Insert sample beneficiaires
INSERT INTO beneficiaire (BEN_IDUCNSS, ASS_MAT, ASS_CLE, PAY_COD, BEN_TYPE, BEN_RANG, BEN_NOM, BEN_PRENOM, BEN_SEXE, BEN_DTNAIS, BEN_TYPID, BEN_NUMID, BEN_EMAIL, BEN_TEL) VALUES
(10000001, 11111111, 11, 216, 1, 1, 'Ben Ali', 'Ahmed', 1, DATE '1985-05-15', 0, '12345678', '<EMAIL>', 20123456);

INSERT INTO beneficiaire (BEN_IDUCNSS, ASS_MAT, ASS_CLE, PAY_COD, BEN_TYPE, BEN_RANG, BEN_NOM, BEN_PRENOM, BEN_SEXE, BEN_DTNAIS, BEN_TYPID, BEN_NUMID, BEN_EMAIL, BEN_TEL) VALUES
(10000002, 22222222, 22, 216, 1, 1, 'Trabelsi', 'Fatma', 2, DATE '1990-08-22', 0, '87654321', '<EMAIL>', 25987654);

INSERT INTO beneficiaire (BEN_IDUCNSS, ASS_MAT, ASS_CLE, PAY_COD, BEN_TYPE, BEN_RANG, BEN_NOM, BEN_PRENOM, BEN_SEXE, BEN_DTNAIS, BEN_TYPID, BEN_NUMID, BEN_EMAIL, BEN_TEL) VALUES
(10000003, 33333333, 33, 216, 2, 1, 'Khelifi', 'Mohamed', 1, DATE '1988-12-03', 0, '11223344', '<EMAIL>', 22334455);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Employeur indexes
CREATE INDEX IDX_EMPLOYEUR_RAIS ON employeur(EMP_RAIS);
CREATE INDEX IDX_EMPLOYEUR_EMAIL ON employeur(EMP_EMAIL);
CREATE INDEX IDX_EMPLOYEUR_ACTIVITE ON employeur(EMP_ACTIVITE);

-- Assure indexes
CREATE INDEX IDX_ASSURE_EMP ON assure(EMP_MAT, EMP_CLE);
CREATE INDEX IDX_ASSURE_DTEFF ON assure(ASS_DTEFF);

-- Beneficiaire indexes
CREATE INDEX IDX_BENEFICIAIRE_ASS ON beneficiaire(ASS_MAT, ASS_CLE);
CREATE INDEX IDX_BENEFICIAIRE_NOM ON beneficiaire(BEN_NOM);
CREATE INDEX IDX_BENEFICIAIRE_PRENOM ON beneficiaire(BEN_PRENOM);
CREATE INDEX IDX_BENEFICIAIRE_EMAIL ON beneficiaire(BEN_EMAIL);
CREATE INDEX IDX_BENEFICIAIRE_TYPE ON beneficiaire(BEN_TYPE);

-- =====================================================
-- FOREIGN KEY CONSTRAINTS (Optional)
-- =====================================================

-- Add foreign key from assure to employeur
ALTER TABLE assure ADD CONSTRAINT FK_ASSURE_EMPLOYEUR 
    FOREIGN KEY (EMP_MAT, EMP_CLE) REFERENCES employeur(EMP_MAT, EMP_CLE);

-- Add foreign key from beneficiaire to assure
ALTER TABLE beneficiaire ADD CONSTRAINT FK_BENEFICIAIRE_ASSURE 
    FOREIGN KEY (ASS_MAT, ASS_CLE) REFERENCES assure(ASS_MAT, ASS_CLE);

-- =====================================================
-- COMMIT CHANGES
-- =====================================================
COMMIT;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check table creation
SELECT table_name FROM user_tables WHERE table_name IN ('EMPLOYEUR', 'ASSURE', 'BENEFICIAIRE');

-- Check data insertion
SELECT 'EMPLOYEUR' as table_name, COUNT(*) as record_count FROM employeur
UNION ALL
SELECT 'ASSURE' as table_name, COUNT(*) as record_count FROM assure
UNION ALL
SELECT 'BENEFICIAIRE' as table_name, COUNT(*) as record_count FROM beneficiaire;

-- Test queries
SELECT e.EMP_RAIS, COUNT(a.ASS_MAT) as nb_assures
FROM employeur e
LEFT JOIN assure a ON e.EMP_MAT = a.EMP_MAT AND e.EMP_CLE = a.EMP_CLE
GROUP BY e.EMP_RAIS;

SELECT a.ASS_MAT, a.ASS_CLE, COUNT(b.BEN_IDUCNSS) as nb_beneficiaires
FROM assure a
LEFT JOIN beneficiaire b ON a.ASS_MAT = b.ASS_MAT AND a.ASS_CLE = b.ASS_CLE
GROUP BY a.ASS_MAT, a.ASS_CLE;
