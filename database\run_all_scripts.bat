@echo off
REM =====================================================
REM CNSS Platform - Oracle Database Setup
REM Batch script to run all SQL scripts in sequence
REM =====================================================

echo ========================================
echo CNSS Platform Database Setup
echo ========================================
echo.

REM Set Oracle connection parameters
set ORACLE_HOST=localhost
set ORACLE_PORT=1521
set ORACLE_SERVICE=XEPDB1
set SYS_PASSWORD=your_sys_password
set CNSS_PASSWORD=CnssPassword123!

echo Step 1: Creating user and tablespace...
echo Please enter SYS password when prompted.
sqlplus sys/%SYS_PASSWORD%@%ORACLE_HOST%:%ORACLE_PORT%/%ORACLE_SERVICE% as sysdba @01_create_user_and_tablespace.sql

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to create user and tablespace
    pause
    exit /b 1
)

echo.
echo Step 2: Creating tables, sequences, and triggers...
sqlplus cnss_user/%CNSS_PASSWORD%@%ORACLE_HOST%:%ORACLE_PORT%/%ORACLE_SERVICE% @02_create_tables.sql

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to create tables
    pause
    exit /b 1
)

echo.
echo Step 3: Creating performance indexes...
sqlplus cnss_user/%CNSS_PASSWORD%@%ORACLE_HOST%:%ORACLE_PORT%/%ORACLE_SERVICE% @03_create_indexes.sql

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to create indexes
    pause
    exit /b 1
)

echo.
echo Step 4: Inserting sample data...
sqlplus cnss_user/%CNSS_PASSWORD%@%ORACLE_HOST%:%ORACLE_PORT%/%ORACLE_SERVICE% @04_insert_sample_data.sql

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to insert sample data
    pause
    exit /b 1
)

echo.
echo Step 5: Creating views and procedures...
sqlplus cnss_user/%CNSS_PASSWORD%@%ORACLE_HOST%:%ORACLE_PORT%/%ORACLE_SERVICE% @05_create_views_and_procedures.sql

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to create views and procedures
    pause
    exit /b 1
)

echo.
echo ========================================
echo Database setup completed successfully!
echo ========================================
echo.
echo Connection details:
echo Host: %ORACLE_HOST%
echo Port: %ORACLE_PORT%
echo Service: %ORACLE_SERVICE%
echo Username: cnss_user
echo Password: %CNSS_PASSWORD%
echo.
echo Connection string for FastAPI:
echo oracle+oracledb://cnss_user:%CNSS_PASSWORD%@%ORACLE_HOST%:%ORACLE_PORT%/%ORACLE_SERVICE%
echo.
echo You can now start your FastAPI backend and React frontend!
echo.
pause
