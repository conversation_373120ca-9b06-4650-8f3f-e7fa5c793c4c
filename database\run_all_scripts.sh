#!/bin/bash
# =====================================================
# CNSS Platform - Oracle Database Setup
# Shell script to run all SQL scripts in sequence
# =====================================================

echo "========================================"
echo "CNSS Platform Database Setup"
echo "========================================"
echo

# Set Oracle connection parameters
ORACLE_HOST="localhost"
ORACLE_PORT="1521"
ORACLE_SERVICE="XEPDB1"
SYS_PASSWORD="your_sys_password"
CNSS_PASSWORD="CnssPassword123!"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check if command was successful
check_status() {
    if [ $? -ne 0 ]; then
        echo -e "${RED}ERROR: $1${NC}"
        exit 1
    else
        echo -e "${GREEN}SUCCESS: $1${NC}"
    fi
}

echo -e "${YELLOW}Step 1: Creating user and tablespace...${NC}"
echo "Please enter SYS password when prompted."
sqlplus sys/${SYS_PASSWORD}@${ORACLE_HOST}:${ORACLE_PORT}/${ORACLE_SERVICE} as sysdba @01_create_user_and_tablespace.sql
check_status "User and tablespace created"

echo
echo -e "${YELLOW}Step 2: Creating tables, sequences, and triggers...${NC}"
sqlplus cnss_user/${CNSS_PASSWORD}@${ORACLE_HOST}:${ORACLE_PORT}/${ORACLE_SERVICE} @02_create_tables.sql
check_status "Tables created"

echo
echo -e "${YELLOW}Step 3: Creating performance indexes...${NC}"
sqlplus cnss_user/${CNSS_PASSWORD}@${ORACLE_HOST}:${ORACLE_PORT}/${ORACLE_SERVICE} @03_create_indexes.sql
check_status "Indexes created"

echo
echo -e "${YELLOW}Step 4: Inserting sample data...${NC}"
sqlplus cnss_user/${CNSS_PASSWORD}@${ORACLE_HOST}:${ORACLE_PORT}/${ORACLE_SERVICE} @04_insert_sample_data.sql
check_status "Sample data inserted"

echo
echo -e "${YELLOW}Step 5: Creating views and procedures...${NC}"
sqlplus cnss_user/${CNSS_PASSWORD}@${ORACLE_HOST}:${ORACLE_PORT}/${ORACLE_SERVICE} @05_create_views_and_procedures.sql
check_status "Views and procedures created"

echo
echo -e "${GREEN}========================================${NC}"
echo -e "${GREEN}Database setup completed successfully!${NC}"
echo -e "${GREEN}========================================${NC}"
echo
echo "Connection details:"
echo "Host: ${ORACLE_HOST}"
echo "Port: ${ORACLE_PORT}"
echo "Service: ${ORACLE_SERVICE}"
echo "Username: cnss_user"
echo "Password: ${CNSS_PASSWORD}"
echo
echo "Connection string for FastAPI:"
echo "oracle+oracledb://cnss_user:${CNSS_PASSWORD}@${ORACLE_HOST}:${ORACLE_PORT}/${ORACLE_SERVICE}"
echo
echo -e "${GREEN}You can now start your FastAPI backend and React frontend!${NC}"
echo
