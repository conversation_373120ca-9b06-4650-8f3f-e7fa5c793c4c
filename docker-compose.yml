version: '3.8'

services:
  # Oracle Database 23c
  oracle-db:
    image: container-registry.oracle.com/database/express:21.3.0-xe
    container_name: cnss_oracle_db
    environment:
      - ORACLE_PWD=OraclePassword123
      - ORACLE_CHARACTERSET=AL32UTF8
    ports:
      - "1521:1521"
      - "5500:5500"
    volumes:
      - oracle_data:/opt/oracle/oradata
      - ./database/init:/opt/oracle/scripts/startup
    networks:
      - cnss_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "sqlplus", "-L", "system/OraclePassword123@//localhost:1521/XE", "@/dev/null"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: cnss_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - cnss_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: cnss_backend
    environment:
      - DATABASE_URL=oracle://cnss_user:cnss_password@oracle-db:1521/XE
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-super-secret-key-change-this-in-production
      - DEBUG=True
      - ENVIRONMENT=development
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - backend_uploads:/app/uploads
    networks:
      - cnss_network
    depends_on:
      - oracle-db
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: cnss_frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8000/api/v1
    ports:
      - "3000:80"
    networks:
      - cnss_network
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Load Balancer (for production)
  nginx:
    image: nginx:alpine
    container_name: cnss_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    networks:
      - cnss_network
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    profiles:
      - production

volumes:
  oracle_data:
    driver: local
  redis_data:
    driver: local
  backend_uploads:
    driver: local

networks:
  cnss_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
