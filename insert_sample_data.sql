-- Sample data for CNSS Platform
-- Clear existing data first
DELETE FROM beneficiaire;
DELETE FROM assure;
DELETE FROM employeur;

-- Insert Employeurs (Employers)
INSERT INTO employeur (emp_mat, emp_cle, emp_rais, emp_sigle, emp_activite, emp_email, emp_tel, emp_dtaff, emp_dtdact) VALUES
(12345678, 12, 'SOCIETE GENERALE TUNISIE', 'SGT', 'BANQUE', '<EMAIL>', '71123456', DATE '2020-01-15', NULL);

INSERT INTO employeur (emp_mat, emp_cle, emp_rais, emp_sigle, emp_activite, emp_email, emp_tel, emp_dtaff, emp_dtdact) VALUES
(87654321, 34, 'TUNISIE TELECOM', 'TT', 'TELECOMMUNICATIONS', '<EMAIL>', '71987654', DATE '2019-03-20', NULL);

INSERT INTO employeur (emp_mat, emp_cle, emp_rais, emp_sigle, emp_activite, emp_email, emp_tel, emp_dtaff, emp_dtdact) VALUES
(11223344, 56, 'ORANGE TUNISIE', 'OT', 'TELECOMMUNICATIONS', '<EMAIL>', '71555666', DATE '2018-06-10', NULL);

INSERT INTO employeur (emp_mat, emp_cle, emp_rais, emp_sigle, emp_activite, emp_email, emp_tel, emp_dtaff, emp_dtdact) VALUES
(99887766, 78, 'BANQUE CENTRALE', 'BCT', 'BANQUE', '<EMAIL>', '71777888', DATE '2015-01-01', NULL);

INSERT INTO employeur (emp_mat, emp_cle, emp_rais, emp_sigle, emp_activite, emp_email, emp_tel, emp_dtaff, emp_dtdact) VALUES
(55443322, 90, 'STEG', 'STEG', 'ENERGIE', '<EMAIL>', '71444555', DATE '2017-05-12', NULL);

-- Insert Assures (Insured Persons)
INSERT INTO assure (ass_mat, ass_cle, emp_mat, emp_cle, ass_dteff, ass_dtimmat, ass_rib, ass_cnrps, etat_chargement) VALUES
(11111111, 11, 12345678, 12, DATE '2020-02-01', DATE '2020-01-20', '12345678901234567890', 'CNRPS123', 'ACTIF');

INSERT INTO assure (ass_mat, ass_cle, emp_mat, emp_cle, ass_dteff, ass_dtimmat, ass_rib, ass_cnrps, etat_chargement) VALUES
(22222222, 22, 87654321, 34, DATE '2019-04-15', DATE '2019-03-25', '09876543210987654321', 'CNRPS456', 'ACTIF');

INSERT INTO assure (ass_mat, ass_cle, emp_mat, emp_cle, ass_dteff, ass_dtimmat, ass_rib, ass_cnrps, etat_chargement) VALUES
(33333333, 33, 11223344, 56, DATE '2018-07-01', DATE '2018-06-15', '11111111111111111111', 'CNRPS789', 'ACTIF');

INSERT INTO assure (ass_mat, ass_cle, emp_mat, emp_cle, ass_dteff, ass_dtimmat, ass_rib, ass_cnrps, etat_chargement) VALUES
(44444444, 44, 99887766, 78, DATE '2015-02-01', DATE '2015-01-10', '22222222222222222222', 'CNRPS101', 'ACTIF');

INSERT INTO assure (ass_mat, ass_cle, emp_mat, emp_cle, ass_dteff, ass_dtimmat, ass_rib, ass_cnrps, etat_chargement) VALUES
(55555555, 55, 55443322, 90, DATE '2017-06-01', DATE '2017-05-20', '33333333333333333333', 'CNRPS202', 'INACTIF');

-- Insert Beneficiaires (Beneficiaries)
INSERT INTO beneficiaire (ben_mat, ben_cle, ben_nom, ben_prenom, ben_email, ben_tel, ben_cin, ben_dtnaiss, ben_dtdeces) VALUES
(10001111, 11, 'BEN ALI', 'MOHAMED', '<EMAIL>', '20123456', '12345678', DATE '1985-03-15', NULL);

INSERT INTO beneficiaire (ben_mat, ben_cle, ben_nom, ben_prenom, ben_email, ben_tel, ben_cin, ben_dtnaiss, ben_dtdeces) VALUES
(10002222, 22, 'TRABELSI', 'FATMA', '<EMAIL>', '25987654', '87654321', DATE '1990-07-22', NULL);

INSERT INTO beneficiaire (ben_mat, ben_cle, ben_nom, ben_prenom, ben_email, ben_tel, ben_cin, ben_dtnaiss, ben_dtdeces) VALUES
(10003333, 33, 'GHARBI', 'AHMED', '<EMAIL>', '22555666', '11223344', DATE '1988-12-10', NULL);

INSERT INTO beneficiaire (ben_mat, ben_cle, ben_nom, ben_prenom, ben_email, ben_tel, ben_cin, ben_dtnaiss, ben_dtdeces) VALUES
(10004444, 44, 'SASSI', 'LEILA', '<EMAIL>', '29777888', '99887766', DATE '1992-05-08', NULL);

INSERT INTO beneficiaire (ben_mat, ben_cle, ben_nom, ben_prenom, ben_email, ben_tel, ben_cin, ben_dtnaiss, ben_dtdeces) VALUES
(10005555, 55, 'BOUAZIZI', 'KARIM', '<EMAIL>', '24444555', '55443322', DATE '1987-09-18', NULL);

INSERT INTO beneficiaire (ben_mat, ben_cle, ben_nom, ben_prenom, ben_email, ben_tel, ben_cin, ben_dtnaiss, ben_dtdeces) VALUES
(10006666, 66, 'JEMLI', 'SONIA', '<EMAIL>', '26111222', '66554433', DATE '1995-01-25', NULL);

-- Commit the changes
COMMIT;

-- Display counts to verify
SELECT 'EMPLOYEURS' as TABLE_NAME, COUNT(*) as COUNT FROM employeur
UNION ALL
SELECT 'ASSURES' as TABLE_NAME, COUNT(*) as COUNT FROM assure
UNION ALL
SELECT 'BENEFICIAIRES' as TABLE_NAME, COUNT(*) as COUNT FROM beneficiaire;
