Microsoft Windows [Version 10.0.26200.5603]
(c) Microsoft Corporation. All rights reserved.

C:\Windows\System32>docker run -d --name oracle-xe -p 1521:1521 -e ORACLE_PWD=password123 gvenzl/oracle-xe:21-slim
docker: error during connect: Head "http://%2F%2F.%2Fpipe%2FdockerDesktopLinuxEngine/_ping": open //./pipe/dockerDesktopLinuxEngine: The system cannot find the file specified.
See 'docker run --help'.

C:\Windows\System32>docker run -d --name oracle-xe -p 1521:1521 -e ORACLE_PWD=password123 gvenzl/oracle-xe:21-slim
Unable to find image 'gvenzl/oracle-xe:21-slim' locally
21-slim: Pulling from gvenzl/oracle-xe
073f36588906: Download complete
d3746687fc59: Download complete
4f8ebc90375b: Download complete
bea495abb2fa: Download complete
3820d7bb3a88: Download complete
f7b0b443446e: Download complete
Digest: sha256:ecdf4302ac3d134e1bac5ef6e0c223c2d0f4d4d2b6d551aa79b2346f1ab8f792
Status: Downloaded newer image for gvenzl/oracle-xe:21-slim
docker: Error response from daemon: Conflict. The container name "/oracle-xe" is already in use by container "51ede8896108cfb0c401c7c337535fbdc1d500812e8ac1668b179357ee478773". You have to remove (or rename) that container to be able to reuse that name.
See 'docker run --help'.

C:\Windows\System32>docker ps
CONTAINER ID   IMAGE     COMMAND   CREATED   STATUS    PORTS     NAMES

C:\Windows\System32>docker start oracle-xe
oracle-xe

C:\Windows\System32>docker ps
CONTAINER ID   IMAGE              COMMAND                  CREATED      STATUS          PORTS                                            NAMES
51ede8896108   gvenzl/oracle-xe   "container-entrypoin…"   3 days ago   Up 10 seconds   0.0.0.0:1521->1521/tcp, 0.0.0.0:8080->8080/tcp   oracle-xe

C:\Windows\System32>docker run -d --name oracle-xe -p 1521:1521 -p 5500:5500 -e ORACLE_PWD=password123 gvenzl/oracle-xe:21-slim
docker: Error response from daemon: Conflict. The container name "/oracle-xe" is already in use by container "51ede8896108cfb0c401c7c337535fbdc1d500812e8ac1668b179357ee478773". You have to remove (or rename) that container to be able to reuse that name.
See 'docker run --help'.

C:\Windows\System32>docker exec -it oracle-xe sqlplus sys/password123@XEPDB1 as sysdba

SQL*Plus: Release ********.0 - Production on Fri May 30 18:05:03 2025
Version ********.0

Copyright (c) 1982, 2021, Oracle.  All rights reserved.

ERROR:
ORA-01017: invalid username/password; logon denied


Enter user-name:
What's next:
    Try Docker Debug for seamless, persistent debugging tools in any container or image → docker debug oracle-xe
    Learn more at https://docs.docker.com/go/debug-cli/


____________
after i see the password with the cmd docker inspect oracle-xe i find the password is oracle so i complete with this:

C:\Windows\System32>docker exec -it oracle-xe sqlplus sys/oracle@XEPDB1 as sysdba

SQL*Plus: Release ********.0 - Production on Fri May 30 18:16:31 2025
Version ********.0

Copyright (c) 1982, 2021, Oracle.  All rights reserved.


Connected to:
Oracle Database 21c Express Edition Release ********.0 - Production
Version ********.0

SQL> CREATE TABLESPACE cnss_data
DATAFILE 'cnss_data.dbf' SIZE 100M
AUTOEXTEND ON NEXT 10M MAXSIZE 1G;

CREATE USER cnss_user IDENTIFIED BY cnss_password
DEFAULT TABLESPACE cnss_data
TEMPORARY TABLESPACE temp;

GRANT CONNECT, RESOURCE, CREATE VIEW TO cnss_user;
GRANT UNLIMITED TABLESPACE TO cnss_user;
  2    3
Tablespace created.

SQL> SQL>   2    3
User created.

SQL> SQL>
Grant succeeded.

SQL>
Grant succeeded.

SQL> sqlplus sys/oracle@localhost:1521/XEPDB1 as sysdba
SP2-0734: unknown command beginning "sqlplus sy..." - rest of line ignored.
SQL> CREATE TABLESPACE cnss_data
DATAFILE 'cnss_data.dbf' SIZE 100M
AUTOEXTEND ON NEXT 10M MAXSIZE 1G;
  4  CREATE USER cnss_user IDENTIFIED BY cnss_password
DEFAULT TABLESPACE cnss_data
TEMPORARY ^C^C


SQL> CREATE USER cnss_user IDENTIFIED BY cnss_password DEFAULT TABLESPACE cnss_data TEMPORARY TABLESPACE temp;

User created.

SQL> GRANT CONNECT, RESOURCE, CREATE VIEW TO cnss_user;
GRANT UNLIMITED TABLESPACE TO cnss_user;
Grant succeeded.

SQL> SELECT tablespace_name FROM dba_tablespaces WHERE tablespace_name = 'CNSS_DATA';
GRANT UNLIMITED TABLESPACE TO cnss_user;SELECT tablespace_name FROM dba_tablespaces WHERE tablespace_name = 'CNSS_DATA'
                                       *
ERROR at line 1:
ORA-00933: SQL command not properly ended


SQL> SELECT tablespace_name FROM dba_tablespaces WHERE tablespace_name = 'CNSS_DATA';

TABLESPACE_NAME
------------------------------
CNSS_DATA

SQL> GRANT CONNECT, RESOURCE, CREATE VIEW TO cnss_user;

Grant succeeded.

SQL> GRANT UNLIMITED TABLESPACE TO cnss_user;

Grant succeeded.

SQL> SELECT username FROM dba_users WHERE username = 'CNSS_USER';

USERNAME
--------------------------------------------------------------------------------
CNSS_USER

SQL> SELECT * FROM dba_role_privs WHERE grantee = 'CNSS_USER';

GRANTEE
--------------------------------------------------------------------------------
GRANTED_ROLE
--------------------------------------------------------------------------------
ADM DEL DEF COM INH
--- --- --- --- ---
CNSS_USER
RESOURCE
NO  NO  YES NO  NO

CNSS_USER
CONNECT
NO  NO  YES NO  NO

GRANTEE
--------------------------------------------------------------------------------
GRANTED_ROLE
--------------------------------------------------------------------------------
ADM DEL DEF COM INH
--- --- --- --- ---


SQL> SELECT * FROM dba_sys_privs WHERE grantee = 'CNSS_USER';

GRANTEE   PRIVILEGE                                ADM COM INH
--------- ---------------------------------------- --- --- ---
CNSS_USER CREATE VIEW                              NO  NO  NO
CNSS_USER UNLIMITED TABLESPACE                     NO  NO  NO

SQL> EXIT;
Disconnected from Oracle Database 21c Express Edition Release ********.0 - Production
Version ********.0

What's next:
    Try Docker Debug for seamless, persistent debugging tools in any container or image → docker debug oracle-xe
    Learn more at https://docs.docker.com/go/debug-cli/

C:\Windows\System32>docker exec -it oracle-xe sqlplus cnss_user/cnss_password@XEPDB1

SQL*Plus: Release ********.0 - Production on Fri May 30 20:03:06 2025
Version ********.0

Copyright (c) 1982, 2021, Oracle.  All rights reserved.


Connected to:
Oracle Database 21c Express Edition Release ********.0 - Production
Version ********.0
