@echo off
setlocal enabledelayedexpansion

REM CNSS Platform Development Setup Script for Windows
REM This script helps set up the development environment on Windows

echo 🏛️ CNSS Platform Development Setup
echo ==================================
echo.

REM Check if Node.js is installed
echo [INFO] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo [SUCCESS] Node.js is installed: !NODE_VERSION!
) else (
    echo [ERROR] Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if Python is installed
echo [INFO] Checking Python installation...
python --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('python --version') do set PYTHON_VERSION=%%i
    echo [SUCCESS] Python is installed: !PYTHON_VERSION!
) else (
    echo [ERROR] Python is not installed. Please install Python 3.11+ from https://python.org/
    pause
    exit /b 1
)

REM Check if Docker is installed
echo [INFO] Checking Docker installation...
docker --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('docker --version') do set DOCKER_VERSION=%%i
    echo [SUCCESS] Docker is installed: !DOCKER_VERSION!
) else (
    echo [WARNING] Docker is not installed. Install Docker for containerized deployment.
)

echo.

REM Setup environment files
echo [INFO] Setting up environment files...

if not exist ".env" (
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo [SUCCESS] Frontend .env file created
    )
)

if not exist "backend\.env" (
    if exist "backend\.env.example" (
        copy "backend\.env.example" "backend\.env" >nul
        echo [SUCCESS] Backend .env file created
    )
)

REM Install frontend dependencies
echo [INFO] Setting up frontend dependencies...

if not exist "package.json" (
    echo [ERROR] package.json not found. Are you in the correct directory?
    pause
    exit /b 1
)

call npm install
if %errorlevel% equ 0 (
    echo [SUCCESS] Frontend dependencies installed successfully!
) else (
    echo [ERROR] Failed to install frontend dependencies!
    pause
    exit /b 1
)

REM Setup backend environment
echo [INFO] Setting up backend environment...

if not exist "backend" (
    echo [ERROR] Backend directory not found. Are you in the correct directory?
    pause
    exit /b 1
)

cd backend

REM Create virtual environment if it doesn't exist
if not exist "venv" (
    echo [INFO] Creating Python virtual environment...
    python -m venv venv
)

REM Activate virtual environment and install dependencies
echo [INFO] Installing Python dependencies...
call venv\Scripts\activate.bat
if exist "requirements.txt" (
    pip install -r requirements.txt
    if %errorlevel% equ 0 (
        echo [SUCCESS] Backend dependencies installed successfully!
    ) else (
        echo [ERROR] Failed to install backend dependencies!
        pause
        exit /b 1
    )
) else (
    echo [ERROR] requirements.txt not found in backend directory!
    pause
    exit /b 1
)

cd ..

echo.
echo [SUCCESS] 🎉 Development environment setup completed!
echo.
echo Next steps:
echo 1. Update environment files with your database credentials
echo 2. Start the frontend: npm start
echo 3. Start the backend: cd backend ^&^& venv\Scripts\activate ^&^& uvicorn app.main:app --reload
echo 4. Or use Docker: docker-compose up -d
echo.
echo Frontend will be available at: http://localhost:3000
echo Backend API will be available at: http://localhost:8000
echo API Documentation will be available at: http://localhost:8000/docs
echo.

pause
