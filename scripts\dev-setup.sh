#!/bin/bash

# CNSS Platform Development Setup Script
# This script helps set up the development environment

set -e

echo "🏛️ CNSS Platform Development Setup"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
check_nodejs() {
    print_status "Checking Node.js installation..."
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js is installed: $NODE_VERSION"
    else
        print_error "Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/"
        exit 1
    fi
}

# Check if Python is installed
check_python() {
    print_status "Checking Python installation..."
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version)
        print_success "Python is installed: $PYTHON_VERSION"
    else
        print_error "Python 3 is not installed. Please install Python 3.11+ from https://python.org/"
        exit 1
    fi
}

# Check if Docker is installed
check_docker() {
    print_status "Checking Docker installation..."
    if command -v docker &> /dev/null; then
        DOCKER_VERSION=$(docker --version)
        print_success "Docker is installed: $DOCKER_VERSION"
    else
        print_warning "Docker is not installed. Install Docker for containerized deployment."
    fi
}

# Install frontend dependencies
setup_frontend() {
    print_status "Setting up frontend dependencies..."
    
    if [ ! -f "package.json" ]; then
        print_error "package.json not found. Are you in the correct directory?"
        exit 1
    fi
    
    npm install
    print_success "Frontend dependencies installed successfully!"
}

# Setup backend environment
setup_backend() {
    print_status "Setting up backend environment..."
    
    if [ ! -d "backend" ]; then
        print_error "Backend directory not found. Are you in the correct directory?"
        exit 1
    fi
    
    cd backend
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        print_status "Creating Python virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Install dependencies
    if [ -f "requirements.txt" ]; then
        print_status "Installing Python dependencies..."
        pip install -r requirements.txt
        print_success "Backend dependencies installed successfully!"
    else
        print_error "requirements.txt not found in backend directory!"
        exit 1
    fi
    
    # Copy environment file if it doesn't exist
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_success "Environment file created from .env.example"
            print_warning "Please update the .env file with your database credentials!"
        else
            print_error ".env.example not found!"
        fi
    fi
    
    cd ..
}

# Setup environment files
setup_env_files() {
    print_status "Setting up environment files..."
    
    # Frontend environment
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_success "Frontend .env file created"
        fi
    fi
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        if [ -f "backend/.env.example" ]; then
            cp backend/.env.example backend/.env
            print_success "Backend .env file created"
        fi
    fi
}

# Main setup function
main() {
    echo ""
    print_status "Starting development environment setup..."
    echo ""
    
    # Check prerequisites
    check_nodejs
    check_python
    check_docker
    
    echo ""
    
    # Setup components
    setup_env_files
    setup_frontend
    setup_backend
    
    echo ""
    print_success "🎉 Development environment setup completed!"
    echo ""
    echo "Next steps:"
    echo "1. Update environment files with your database credentials"
    echo "2. Start the frontend: npm start"
    echo "3. Start the backend: cd backend && source venv/bin/activate && uvicorn app.main:app --reload"
    echo "4. Or use Docker: docker-compose up -d"
    echo ""
    echo "Frontend will be available at: http://localhost:3000"
    echo "Backend API will be available at: http://localhost:8000"
    echo "API Documentation will be available at: http://localhost:8000/docs"
    echo ""
}

# Run main function
main
