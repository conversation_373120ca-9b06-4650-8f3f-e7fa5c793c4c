@echo off
echo ========================================
echo CNSS Platform - Create Database Tables
echo ========================================
echo.

echo Checking if Oracle container is running...
docker ps | findstr oracle-xe >nul
if %errorlevel% neq 0 (
    echo ❌ ERROR: Oracle container is not running
    echo Please start Oracle first:
    echo   docker start oracle-xe
    pause
    exit /b 1
)

echo ✅ Oracle container is running

echo.
echo Testing connection to Oracle...
docker exec oracle-xe sqlplus -s cnss_user/cnss_password@XEPDB1 @/dev/null <nul >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Cannot connect to Oracle as cnss_user
    echo Please verify your Oracle setup:
    echo   docker exec -it oracle-xe sqlplus cnss_user/cnss_password@XEPDB1
    pause
    exit /b 1
)

echo ✅ Connection to Oracle successful

echo.
echo Creating CNSS tables in Oracle database...
echo This will create:
echo - EMPLOYEUR table
echo - ASSURE table  
echo - BENEFICIAIRE table
echo.

echo Running SQL script...
docker exec -i oracle-xe sqlplus -s cnss_user/cnss_password@XEPDB1 < create_cnss_tables.sql

if %errorlevel% neq 0 (
    echo ❌ ERROR: Failed to create tables
    echo Please check the SQL script and try again
    pause
    exit /b 1
)

echo.
echo ✅ Tables created successfully!

echo.
echo Verifying table creation...
docker exec -i oracle-xe sqlplus -s cnss_user/cnss_password@XEPDB1 << EOF
SET PAGESIZE 50
SET LINESIZE 100
COLUMN table_name FORMAT A20
COLUMN num_rows FORMAT 999999

SELECT table_name, num_rows 
FROM user_tables 
WHERE table_name IN ('EMPLOYEUR', 'ASSURE', 'BENEFICIAIRE')
ORDER BY table_name;

EXIT;
EOF

echo.
echo ========================================
echo 🎉 CNSS Database Setup Complete!
echo ========================================
echo.
echo Your Oracle database now contains:
echo - EMPLOYEUR table with sample companies
echo - ASSURE table with sample employees  
echo - BENEFICIAIRE table with sample beneficiaries
echo.
echo Database Connection Details:
echo - Host: localhost:1521
echo - Service: XEPDB1
echo - Username: cnss_user
echo - Password: cnss_password
echo.
echo Next steps:
echo 1. Test the backend connection:
echo    cd backend
echo    python test_oracle_connection.py
echo.
echo 2. Start the backend server:
echo    .\start_backend.bat
echo.
echo 3. Start the frontend:
echo    npm start
echo.
pause
