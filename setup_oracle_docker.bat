@echo off
echo ========================================
echo CNSS Platform - Oracle Database Setup
echo ========================================
echo.

echo Step 1: Checking if Dock<PERSON> is running...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not installed or not running
    echo Please install Docker Desktop and make sure it's running
    pause
    exit /b 1
)
echo ✅ Docker is available

echo.
echo Step 2: Stopping any existing Oracle container...
docker stop oracle-xe >nul 2>&1
docker rm oracle-xe >nul 2>&1
echo ✅ Cleaned up existing containers

echo.
echo Step 3: Starting Oracle Database in Docker...
echo This may take a few minutes to download the image...
docker run -d --name oracle-xe -p 1521:1521 -p 5500:5500 -e ORACLE_PWD=password123 gvenzl/oracle-xe:21-slim

if %errorlevel% neq 0 (
    echo ERROR: Failed to start Oracle container
    pause
    exit /b 1
)

echo ✅ Oracle container started successfully

echo.
echo Step 4: Waiting for Oracle to be ready...
echo This may take 2-3 minutes...
timeout /t 30 /nobreak >nul

:check_oracle
echo Checking Oracle status...
docker exec oracle-xe sqlplus -s sys/password123@XEPDB1 as sysdba @/dev/null <nul >nul 2>&1
if %errorlevel% neq 0 (
    echo Oracle is still starting up... waiting 15 more seconds
    timeout /t 15 /nobreak >nul
    goto check_oracle
)

echo ✅ Oracle is ready!

echo.
echo Step 5: Creating CNSS user and database...
docker exec -i oracle-xe sqlplus -s sys/password123@XEPDB1 as sysdba << EOF
-- Create tablespace
CREATE TABLESPACE cnss_data
DATAFILE 'cnss_data.dbf' SIZE 100M
AUTOEXTEND ON NEXT 10M MAXSIZE 1G;

-- Create user
CREATE USER cnss_user IDENTIFIED BY cnss_password
DEFAULT TABLESPACE cnss_data
TEMPORARY TABLESPACE temp;

-- Grant privileges
GRANT CONNECT, RESOURCE, CREATE VIEW TO cnss_user;
GRANT UNLIMITED TABLESPACE TO cnss_user;

-- Connect as cnss_user and create tables
CONNECT cnss_user/cnss_password@XEPDB1;

-- Create EMPLOYEUR table
CREATE TABLE employeur (
    emp_mat NUMBER(8) NOT NULL,
    emp_cle NUMBER(2) NOT NULL,
    emp_rais VARCHAR2(100),
    emp_sigle VARCHAR2(20),
    emp_activite VARCHAR2(100),
    emp_email VARCHAR2(100),
    emp_tel VARCHAR2(20),
    emp_dtaff DATE,
    emp_dtdact DATE,
    CONSTRAINT pk_employeur PRIMARY KEY (emp_mat, emp_cle)
);

-- Create ASSURE table
CREATE TABLE assure (
    ass_mat NUMBER(8) NOT NULL,
    ass_cle NUMBER(2) NOT NULL,
    emp_mat NUMBER(8),
    emp_cle NUMBER(2),
    ass_dteff DATE,
    ass_dtimmat DATE,
    ass_rib VARCHAR2(24),
    ass_cnrps VARCHAR2(20),
    etat_chargement VARCHAR2(20),
    CONSTRAINT pk_assure PRIMARY KEY (ass_mat, ass_cle),
    CONSTRAINT fk_assure_employeur FOREIGN KEY (emp_mat, emp_cle) 
        REFERENCES employeur(emp_mat, emp_cle)
);

-- Create BENEFICIAIRE table
CREATE TABLE beneficiaire (
    ben_mat NUMBER(8) NOT NULL,
    ben_cle NUMBER(2) NOT NULL,
    ben_nom VARCHAR2(50),
    ben_prenom VARCHAR2(50),
    ben_email VARCHAR2(100),
    ben_tel VARCHAR2(20),
    ben_cin VARCHAR2(8),
    ben_dtnaiss DATE,
    ben_dtdeces DATE,
    CONSTRAINT pk_beneficiaire PRIMARY KEY (ben_mat, ben_cle)
);

-- Insert sample data
INSERT INTO employeur VALUES (12345678, 12, 'SOCIETE GENERALE TUNISIE', 'SGT', 'BANQUE', '<EMAIL>', '71123456', DATE '2020-01-15', NULL);
INSERT INTO employeur VALUES (87654321, 34, 'TUNISIE TELECOM', 'TT', 'TELECOMMUNICATIONS', '<EMAIL>', '71987654', DATE '2019-03-20', NULL);
INSERT INTO employeur VALUES (11223344, 56, 'ORANGE TUNISIE', 'OT', 'TELECOMMUNICATIONS', '<EMAIL>', '71555666', DATE '2018-06-10', NULL);
INSERT INTO employeur VALUES (99887766, 78, 'BANQUE CENTRALE', 'BCT', 'BANQUE', '<EMAIL>', '71777888', DATE '2015-01-01', NULL);

INSERT INTO assure VALUES (11111111, 11, 12345678, 12, DATE '2020-02-01', DATE '2020-01-20', 'TN59 1234 5678 9012 3456 7890', 'CNRPS123', 'ACTIF');
INSERT INTO assure VALUES (22222222, 22, 87654321, 34, DATE '2019-04-01', DATE '2019-03-25', 'TN59 9876 5432 1098 7654 3210', 'CNRPS456', 'ACTIF');
INSERT INTO assure VALUES (33333333, 33, 11223344, 56, DATE '2018-07-15', DATE '2018-06-20', 'TN59 1111 2222 3333 4444 5555', 'CNRPS789', 'ACTIF');
INSERT INTO assure VALUES (44444444, 44, 99887766, 78, DATE '2015-02-01', DATE '2015-01-15', 'TN59 6666 7777 8888 9999 0000', 'CNRPS012', 'ACTIF');

INSERT INTO beneficiaire VALUES (33333333, 33, 'BEN ALI', 'MOHAMED', '<EMAIL>', '98123456', '12345678', DATE '1985-05-15', NULL);
INSERT INTO beneficiaire VALUES (44444444, 44, 'TRABELSI', 'FATMA', '<EMAIL>', '97987654', '87654321', DATE '1990-08-22', NULL);
INSERT INTO beneficiaire VALUES (55555555, 55, 'GHARBI', 'AHMED', '<EMAIL>', '96111222', '11223344', DATE '1988-12-03', NULL);
INSERT INTO beneficiaire VALUES (66666666, 66, 'SASSI', 'LEILA', '<EMAIL>', '95333444', '55667788', DATE '1992-03-18', NULL);

COMMIT;

-- Show table counts
SELECT 'EMPLOYEUR' as TABLE_NAME, COUNT(*) as COUNT FROM employeur
UNION ALL
SELECT 'ASSURE' as TABLE_NAME, COUNT(*) as COUNT FROM assure
UNION ALL
SELECT 'BENEFICIAIRE' as TABLE_NAME, COUNT(*) as COUNT FROM beneficiaire;

EXIT;
EOF

if %errorlevel% neq 0 (
    echo ERROR: Failed to create database schema
    pause
    exit /b 1
)

echo ✅ Database schema created successfully

echo.
echo ========================================
echo 🎉 ORACLE SETUP COMPLETE!
echo ========================================
echo.
echo Database Details:
echo - Host: localhost
echo - Port: 1521
echo - Service: XEPDB1
echo - Username: cnss_user
echo - Password: cnss_password
echo.
echo Connection String for FastAPI:
echo DATABASE_URL = "oracle+cx_oracle://cnss_user:cnss_password@localhost:1521/XEPDB1"
echo.
echo To connect manually:
echo docker exec -it oracle-xe sqlplus cnss_user/cnss_password@XEPDB1
echo.
echo To stop Oracle:
echo docker stop oracle-xe
echo.
echo To start Oracle again:
echo docker start oracle-xe
echo.
echo Your CNSS platform is now ready with Oracle database! 🚀
echo.
pause
