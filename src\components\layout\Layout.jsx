import React, { useState } from 'react';
import { Outlet } from 'react-router-dom';
import { motion } from 'framer-motion';
import Navbar from './Navbar';
import Sidebar from './Sidebar';

/**
 * Main Layout Component
 * Provides the overall structure for authenticated pages
 */
const Layout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarVisible, setSidebarVisible] = useState(true); // For desktop toggle

  const toggleSidebar = () => {
    if (window.innerWidth >= 1024) {
      // Desktop: toggle visibility
      setSidebarVisible(!sidebarVisible);
    } else {
      // Mobile: toggle open/close
      setSidebarOpen(!sidebarOpen);
    }
  };

  const closeSidebar = () => {
    setSidebarOpen(false);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Sidebar */}
      {sidebarVisible && (
        <Sidebar isOpen={sidebarOpen} onClose={closeSidebar} />
      )}

      {/* Main content area */}
      <div className={`min-h-screen transition-all duration-300 ${
        sidebarVisible ? 'lg:ml-64' : 'lg:ml-0'
      }`}>
        {/* Navbar */}
        <Navbar onMenuToggle={toggleSidebar} />

        {/* Page content */}
        <motion.main
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="p-6"
        >
          <Outlet />
        </motion.main>
      </div>
    </div>
  );
};

export default Layout;
