import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  HomeIcon,
  BuildingOfficeIcon,
  CogIcon,
  ChartBarIcon,
  UsersIcon,
  DocumentTextIcon,
  ShieldCheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../../contexts/AuthContext';

/**
 * Sidebar Navigation Component
 * Responsive sidebar with role-based navigation
 */
const Sidebar = ({ isOpen, onClose }) => {
  const { user, hasAnyRole } = useAuth();
  const location = useLocation();

  // Navigation items with role-based access
  const navigationItems = [
    {
      name: 'Tableau de bord',
      href: '/dashboard',
      icon: HomeIcon,
      roles: ['admin', 'agent']
    },
    {
      name: 'Employeurs',
      href: '/employers',
      icon: BuildingOfficeIcon,
      roles: ['admin', 'agent']
    },
    {
      name: 'Services',
      href: '/services',
      icon: CogIcon,
      roles: ['admin', 'agent']
    },
    {
      name: 'Échanges API',
      href: '/api-exchanges',
      icon: DocumentTextIcon,
      roles: ['admin', 'agent']
    },
    {
      name: 'Utilisateurs',
      href: '/users',
      icon: UsersIcon,
      roles: ['admin']
    },
    {
      name: 'Rapports',
      href: '/reports',
      icon: ChartBarIcon,
      roles: ['admin']
    },
    {
      name: 'Administration',
      href: '/admin',
      icon: ShieldCheckIcon,
      roles: ['admin']
    },
    {
      name: 'Paramètres',
      href: '/settings',
      icon: CogIcon,
      roles: ['admin']
    }
  ];

  // Filter navigation items based on user role
  const filteredNavigation = navigationItems;

  const sidebarVariants = {
    open: {
      x: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 40
      }
    },
    closed: {
      x: "-100%",
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 40
      }
    }
  };



  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <motion.div
        variants={sidebarVariants}
        animate="open"
        className="fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 shadow-soft lg:translate-x-0"
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-primary-600 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold">C</span>
              </div>
              <div className="ml-3">
                <h2 className="text-lg font-semibold text-gray-900">CNSS</h2>
                <p className="text-xs text-gray-500">Platform</p>
              </div>
            </div>

            {/* Close button for mobile */}
            <button
              onClick={onClose}
              className="lg:hidden p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {filteredNavigation.map((item, index) => {
              const isActive = location.pathname === item.href ||
                             location.pathname.startsWith(item.href + '/');

              return (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <NavLink
                    to={item.href}
                    onClick={onClose}
                    className={({ isActive: linkActive }) => {
                      const active = isActive || linkActive;
                      return `
                        group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200
                        ${active
                          ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-600'
                          : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                        }
                      `;
                    }}
                  >
                    <item.icon
                      className={`mr-3 h-5 w-5 transition-colors duration-200 ${
                        isActive ? 'text-primary-600' : 'text-gray-500 group-hover:text-gray-700'
                      }`}
                    />
                    {item.name}
                  </NavLink>
                </motion.div>
              );
            })}
          </nav>

          {/* User info */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-gray-200 rounded-xl flex items-center justify-center">
                <span className="text-gray-600 font-medium text-sm">
                  {user?.first_name?.[0]}{user?.last_name?.[0]}
                </span>
              </div>
              <div className="ml-3 flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {user?.first_name} {user?.last_name}
                </p>
                <p className="text-xs text-gray-500 capitalize">
                  {user?.role}
                </p>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </>
  );
};

export default Sidebar;
