import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  BuildingOfficeIcon,
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import Modal, { ModalFooter } from '../ui/Modal';
import Button from '../ui/Button';
import Input from '../ui/Input';
import { employersAPI } from '../../services/api';
import toast from 'react-hot-toast';

/**
 * Employer Modal Component
 * Handles create, edit, and view modes for employers
 */
const EmployerModal = ({
  isOpen,
  onClose,
  mode = 'view', // 'view', 'create', 'edit'
  employer = null,
  onSuccess
}) => {
  const [formData, setFormData] = useState({
    company_name: '',
    registration_number: '',
    tax_id: '',
    cnss_number: '',
    email: '',
    phone: '',
    address_line1: '',
    city: '',
    postal_code: '',
    region: '',
    legal_rep_name: '',
    legal_rep_title: ''
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Initialize form data when employer changes
  useEffect(() => {
    if (employer && (mode === 'edit' || mode === 'view')) {
      setFormData({
        company_name: employer.company_name || '',
        registration_number: employer.registration_number || '',
        tax_id: employer.tax_id || '',
        cnss_number: employer.cnss_number || '',
        email: employer.email || '',
        phone: employer.phone || '',
        address_line1: employer.address_line1 || '',
        city: employer.city || '',
        postal_code: employer.postal_code || '',
        region: employer.region || '',
        legal_rep_name: employer.legal_rep_name || '',
        legal_rep_title: employer.legal_rep_title || ''
      });
    } else if (mode === 'create') {
      setFormData({
        company_name: '',
        registration_number: '',
        tax_id: '',
        cnss_number: '',
        email: '',
        phone: '',
        address_line1: '',
        city: '',
        postal_code: '',
        region: '',
        legal_rep_name: '',
        legal_rep_title: ''
      });
    }
    setErrors({});
  }, [employer, mode]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.company_name.trim()) {
      newErrors.company_name = 'Le nom de l\'entreprise est requis';
    }
    if (!formData.registration_number.trim()) {
      newErrors.registration_number = 'Le numéro d\'enregistrement est requis';
    }
    if (!formData.tax_id.trim()) {
      newErrors.tax_id = 'L\'identifiant fiscal est requis';
    }
    if (!formData.cnss_number.trim()) {
      newErrors.cnss_number = 'Le numéro CNSS est requis';
    }
    if (!formData.email.trim()) {
      newErrors.email = 'L\'email est requis';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide';
    }
    if (!formData.legal_rep_name.trim()) {
      newErrors.legal_rep_name = 'Le nom du représentant légal est requis';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      if (mode === 'create') {
        await employersAPI.create(formData);
        toast.success('Employeur créé avec succès');
      } else if (mode === 'edit') {
        await employersAPI.update(employer.id, formData);
        toast.success('Employeur modifié avec succès');
      }
      
      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error saving employer:', error);
      toast.error('Erreur lors de la sauvegarde');
    } finally {
      setLoading(false);
    }
  };

  const getModalTitle = () => {
    switch (mode) {
      case 'create':
        return 'Nouvel Employeur';
      case 'edit':
        return 'Modifier Employeur';
      case 'view':
        return 'Détails Employeur';
      default:
        return 'Employeur';
    }
  };

  const isReadOnly = mode === 'view';

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={getModalTitle()}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Company Information */}
        <div>
          <h4 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <BuildingOfficeIcon className="w-5 h-5 mr-2" />
            Informations de l'entreprise
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Nom de l'entreprise"
              value={formData.company_name}
              onChange={(e) => handleInputChange('company_name', e.target.value)}
              error={errors.company_name}
              disabled={isReadOnly}
              required
            />
            <Input
              label="Numéro d'enregistrement"
              value={formData.registration_number}
              onChange={(e) => handleInputChange('registration_number', e.target.value)}
              error={errors.registration_number}
              disabled={isReadOnly}
              required
            />
            <Input
              label="Identifiant fiscal"
              value={formData.tax_id}
              onChange={(e) => handleInputChange('tax_id', e.target.value)}
              error={errors.tax_id}
              disabled={isReadOnly}
              required
            />
            <Input
              label="Numéro CNSS"
              value={formData.cnss_number}
              onChange={(e) => handleInputChange('cnss_number', e.target.value)}
              error={errors.cnss_number}
              disabled={isReadOnly}
              required
            />
          </div>
        </div>

        {/* Contact Information */}
        <div>
          <h4 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <EnvelopeIcon className="w-5 h-5 mr-2" />
            Informations de contact
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              error={errors.email}
              disabled={isReadOnly}
              required
            />
            <Input
              label="Téléphone"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              error={errors.phone}
              disabled={isReadOnly}
            />
          </div>
        </div>

        {/* Address Information */}
        <div>
          <h4 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <MapPinIcon className="w-5 h-5 mr-2" />
            Adresse
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <Input
                label="Adresse"
                value={formData.address_line1}
                onChange={(e) => handleInputChange('address_line1', e.target.value)}
                disabled={isReadOnly}
              />
            </div>
            <Input
              label="Ville"
              value={formData.city}
              onChange={(e) => handleInputChange('city', e.target.value)}
              disabled={isReadOnly}
            />
            <Input
              label="Code postal"
              value={formData.postal_code}
              onChange={(e) => handleInputChange('postal_code', e.target.value)}
              disabled={isReadOnly}
            />
            <Input
              label="Région"
              value={formData.region}
              onChange={(e) => handleInputChange('region', e.target.value)}
              disabled={isReadOnly}
            />
          </div>
        </div>

        {/* Legal Representative */}
        <div>
          <h4 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <UserIcon className="w-5 h-5 mr-2" />
            Représentant légal
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Nom du représentant"
              value={formData.legal_rep_name}
              onChange={(e) => handleInputChange('legal_rep_name', e.target.value)}
              error={errors.legal_rep_name}
              disabled={isReadOnly}
              required
            />
            <Input
              label="Titre/Fonction"
              value={formData.legal_rep_title}
              onChange={(e) => handleInputChange('legal_rep_title', e.target.value)}
              disabled={isReadOnly}
            />
          </div>
        </div>

        {/* Footer */}
        <ModalFooter>
          <Button
            type="button"
            variant="secondary"
            onClick={onClose}
          >
            {mode === 'view' ? 'Fermer' : 'Annuler'}
          </Button>
          {!isReadOnly && (
            <Button
              type="submit"
              loading={loading}
              disabled={loading}
            >
              {mode === 'create' ? 'Créer' : 'Modifier'}
            </Button>
          )}
        </ModalFooter>
      </form>
    </Modal>
  );
};

export default EmployerModal;
