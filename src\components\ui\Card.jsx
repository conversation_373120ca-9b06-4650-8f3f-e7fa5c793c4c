import React from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../../contexts/ThemeContext';

/**
 * Reusable Card component with modern soft design
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Card content
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.hover - Enable hover effects
 * @param {Function} props.onClick - Click handler
 */
const Card = ({
  children,
  className = '',
  hover = false,
  onClick,
  ...props
}) => {
  const { isDark } = useTheme();

  const baseClasses = `rounded-2xl shadow-soft p-6 transition-colors duration-300 ${
    isDark
      ? 'bg-gray-800 border border-gray-700'
      : 'bg-white border border-gray-100'
  }`;
  const hoverClasses = hover ? 'hover:shadow-soft-lg transition-all duration-200 cursor-pointer' : '';
  const classes = `${baseClasses} ${hoverClasses} ${className}`;

  const CardComponent = onClick ? motion.div : 'div';
  const motionProps = onClick ? {
    whileHover: { scale: 1.02, y: -2 },
    whileTap: { scale: 0.98 },
    transition: { duration: 0.2 }
  } : {};

  return (
    <CardComponent
      className={classes}
      onClick={onClick}
      {...motionProps}
      {...props}
    >
      {children}
    </CardComponent>
  );
};

/**
 * Card Header component
 */
const CardHeader = ({ children, className = '' }) => (
  <div className={`mb-4 ${className}`}>
    {children}
  </div>
);

/**
 * Card Title component
 */
const CardTitle = ({ children, className = '' }) => {
  const { isDark } = useTheme();
  return (
    <h3 className={`text-lg font-semibold transition-colors duration-300 ${
      isDark ? 'text-white' : 'text-gray-900'
    } ${className}`}>
      {children}
    </h3>
  );
};

/**
 * Card Content component
 */
const CardContent = ({ children, className = '' }) => (
  <div className={className}>
    {children}
  </div>
);

/**
 * Card Footer component
 */
const CardFooter = ({ children, className = '' }) => {
  const { isDark } = useTheme();
  return (
    <div className={`mt-4 pt-4 border-t transition-colors duration-300 ${
      isDark ? 'border-gray-700' : 'border-gray-100'
    } ${className}`}>
      {children}
    </div>
  );
};

Card.Header = CardHeader;
Card.Title = CardTitle;
Card.Content = CardContent;
Card.Footer = CardFooter;

export default Card;
