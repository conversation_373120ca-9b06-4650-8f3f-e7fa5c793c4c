import React, { forwardRef } from 'react';
import { ExclamationCircleIcon } from '@heroicons/react/24/outline';

/**
 * Reusable Input component with modern soft design
 * @param {Object} props - Component props
 * @param {string} props.label - Input label
 * @param {string} props.error - Error message
 * @param {string} props.placeholder - Input placeholder
 * @param {string} props.type - Input type
 * @param {boolean} props.required - Required field
 * @param {string} props.className - Additional CSS classes
 */
const Input = forwardRef(({
  label,
  error,
  placeholder,
  type = 'text',
  required = false,
  className = '',
  ...props
}, ref) => {
  const inputClasses = `
    w-full px-4 py-3 border rounded-xl transition-all duration-200 bg-white shadow-inner-soft
    focus:ring-2 focus:ring-offset-0 focus:outline-none
    ${error 
      ? 'border-error-300 focus:border-error-500 focus:ring-error-500' 
      : 'border-gray-300 focus:border-primary-500 focus:ring-primary-500'
    }
    ${className}
  `;

  return (
    <div className="space-y-2">
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-error-500 ml-1">*</span>}
        </label>
      )}
      <div className="relative">
        <input
          ref={ref}
          type={type}
          placeholder={placeholder}
          className={inputClasses}
          {...props}
        />
        {error && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <ExclamationCircleIcon className="h-5 w-5 text-error-500" />
          </div>
        )}
      </div>
      {error && (
        <p className="text-sm text-error-600 flex items-center">
          <ExclamationCircleIcon className="h-4 w-4 mr-1" />
          {error}
        </p>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;
