import React, { createContext, useContext, useReducer, useEffect } from 'react';

// Theme actions
const THEME_ACTIONS = {
  SET_THEME: 'SET_THEME',
  TOGGLE_THEME: 'TOGGLE_THEME',
  SET_SYSTEM_PREFERENCE: 'SET_SYSTEM_PREFERENCE'
};

// Theme reducer
const themeReducer = (state, action) => {
  switch (action.type) {
    case THEME_ACTIONS.SET_THEME:
      return {
        ...state,
        theme: action.payload,
        systemPreference: action.payload === 'system'
      };
    case THEME_ACTIONS.TOGGLE_THEME:
      const newTheme = state.theme === 'light' ? 'dark' : 'light';
      return {
        ...state,
        theme: newTheme,
        systemPreference: false
      };
    case THEME_ACTIONS.SET_SYSTEM_PREFERENCE:
      return {
        ...state,
        systemPreference: action.payload
      };
    default:
      return state;
  }
};

// Initial state
const initialState = {
  theme: 'light', // 'light', 'dark', 'system'
  systemPreference: false,
  isDark: false
};

// Create context
const ThemeContext = createContext();

/**
 * Custom hook to use theme context
 */
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

/**
 * Theme Provider Component
 * Manages theme state and provides theme methods
 */
export const ThemeProvider = ({ children }) => {
  const [state, dispatch] = useReducer(themeReducer, initialState);

  // Get system preference
  const getSystemPreference = () => {
    if (typeof window !== 'undefined') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return 'light';
  };

  // Apply theme to document
  const applyTheme = (theme) => {
    const root = document.documentElement;
    const isDark = theme === 'dark' || (theme === 'system' && getSystemPreference() === 'dark');
    
    if (isDark) {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
    
    return isDark;
  };

  // Initialize theme from localStorage
  useEffect(() => {
    const savedTheme = localStorage.getItem('cnss_theme') || 'light';
    const isDark = applyTheme(savedTheme);
    
    dispatch({
      type: THEME_ACTIONS.SET_THEME,
      payload: savedTheme
    });

    // Update state with computed isDark value
    state.isDark = isDark;
  }, []);

  // Listen for system preference changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      const handleChange = () => {
        if (state.theme === 'system') {
          const isDark = applyTheme('system');
          state.isDark = isDark;
        }
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [state.theme]);

  // Apply theme when it changes
  useEffect(() => {
    const isDark = applyTheme(state.theme);
    localStorage.setItem('cnss_theme', state.theme);
    state.isDark = isDark;
  }, [state.theme]);

  /**
   * Set theme
   * @param {string} theme - Theme to set ('light', 'dark', 'system')
   */
  const setTheme = (theme) => {
    dispatch({
      type: THEME_ACTIONS.SET_THEME,
      payload: theme
    });
  };

  /**
   * Toggle between light and dark theme
   */
  const toggleTheme = () => {
    dispatch({
      type: THEME_ACTIONS.TOGGLE_THEME
    });
  };

  /**
   * Get current effective theme (resolves 'system' to actual theme)
   */
  const getEffectiveTheme = () => {
    if (state.theme === 'system') {
      return getSystemPreference();
    }
    return state.theme;
  };

  /**
   * Check if current theme is dark
   */
  const isDarkMode = () => {
    const effectiveTheme = getEffectiveTheme();
    return effectiveTheme === 'dark';
  };

  const value = {
    theme: state.theme,
    isDark: isDarkMode(),
    systemPreference: state.systemPreference,
    setTheme,
    toggleTheme,
    getEffectiveTheme,
    isDarkMode
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeContext;
