@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply font-inter bg-gray-50 text-gray-900 antialiased;
  }

  * {
    @apply border-gray-200;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2.5 px-6 rounded-xl transition-all duration-200 shadow-soft hover:shadow-soft-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-white hover:bg-gray-50 text-gray-700 font-medium py-2.5 px-6 rounded-xl border border-gray-300 transition-all duration-200 shadow-soft hover:shadow-soft-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-danger {
    @apply bg-error-600 hover:bg-error-700 text-white font-medium py-2.5 px-6 rounded-xl transition-all duration-200 shadow-soft hover:shadow-soft-lg focus:outline-none focus:ring-2 focus:ring-error-500 focus:ring-offset-2;
  }

  .input-field {
    @apply w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 bg-white shadow-inner-soft;
  }

  .card {
    @apply bg-white rounded-2xl shadow-soft border border-gray-100 p-6;
  }

  .card-hover {
    @apply bg-white rounded-2xl shadow-soft border border-gray-100 p-6 hover:shadow-soft-lg transition-all duration-200 cursor-pointer;
  }
}
