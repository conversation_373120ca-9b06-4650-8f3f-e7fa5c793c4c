import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  BuildingOfficeIcon,
  UsersIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import Card from '../components/ui/Card';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';

/**
 * Dashboard Page Component
 * Main dashboard with statistics and recent activity
 */
const Dashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState(null);
  const [recentActivity, setRecentActivity] = useState([]);
  const [systemHealth, setSystemHealth] = useState(null);
  const [loading, setLoading] = useState(true);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch real statistics from Oracle tables
        try {
          const [employeursStats, assuresStats, beneficiairesStats] = await Promise.all([
            axios.get('/api/v1/employeurs/statistics'),
            axios.get('/api/v1/assures/statistics'),
            axios.get('/api/v1/beneficiaires/statistics')
          ]);

          const realStats = {
            employeurs: {
              total: employeursStats.data.total || 0,
              change: '+0%',
              active: employeursStats.data.active || 0,
              inactive: employeursStats.data.inactive || 0
            },
            assures: {
              total: assuresStats.data.total || 0,
              change: '+0%',
              withEmployer: assuresStats.data.with_employer || 0,
              withRib: assuresStats.data.with_rib || 0
            },
            beneficiaires: {
              total: beneficiairesStats.data.total || 0,
              change: '+0%',
              male: beneficiairesStats.data.male || 0,
              female: beneficiairesStats.data.female || 0
            }
          };

          setStats(realStats);

          // Mock activity for now
          const mockActivity = [
            {
              id: 1,
              message: `${realStats.employeurs.total} employeurs dans la base de données`,
              timestamp: new Date().toISOString(),
              status: 'success'
            },
            {
              id: 2,
              message: `${realStats.assures.total} assurés enregistrés`,
              timestamp: new Date().toISOString(),
              status: 'success'
            },
            {
              id: 3,
              message: `${realStats.beneficiaires.total} bénéficiaires dans le système`,
              timestamp: new Date().toISOString(),
              status: 'success'
            }
          ];

          setRecentActivity(mockActivity);

          // Mock system health
          const mockSystemHealth = {
            database: { status: 'healthy', responseTime: '12ms' },
            apiGateway: { status: 'healthy', responseTime: '45ms' },
            oracleDB: { status: 'healthy', responseTime: '23ms' },
            storage: { status: 'healthy', usage: '67%' }
          };

          setSystemHealth(mockSystemHealth);

        } catch (apiError) {
          console.log('API not available, using fallback data');

          // Fallback to mock data
          const mockStats = {
            employeurs: { total: 0, change: '+0%', active: 0, inactive: 0 },
            assures: { total: 0, change: '+0%', withEmployer: 0, withRib: 0 },
            beneficiaires: { total: 0, change: '+0%', male: 0, female: 0 }
          };

          const mockActivity = [
            {
              id: 1,
              message: 'Connexion à la base de données Oracle en cours...',
              timestamp: new Date().toISOString(),
              status: 'warning'
            }
          ];

          const mockSystemHealth = {
            database: { status: 'warning', responseTime: 'N/A' },
            apiGateway: { status: 'warning', responseTime: 'N/A' },
            oracleDB: { status: 'warning', responseTime: 'N/A' },
            storage: { status: 'healthy', usage: '67%' }
          };

          setStats(mockStats);
          setRecentActivity(mockActivity);
          setSystemHealth(mockSystemHealth);
        }

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon className="h-5 w-5 text-success-500" />;
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-warning-500" />;
      case 'error':
        return <ExclamationTriangleIcon className="h-5 w-5 text-error-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-primary-500" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  const statCards = [
    {
      title: 'Employeurs',
      value: stats?.employeurs.total || 0,
      change: stats?.employeurs.change || '0%',
      icon: BuildingOfficeIcon,
      color: 'primary',
      subtitle: `${stats?.employeurs.active || 0} actifs`
    },
    {
      title: 'Assurés',
      value: stats?.assures.total || 0,
      change: stats?.assures.change || '0%',
      icon: UsersIcon,
      color: 'success',
      subtitle: `${stats?.assures.withEmployer || 0} avec employeur`
    },
    {
      title: 'Bénéficiaires',
      value: stats?.beneficiaires.total || 0,
      change: stats?.beneficiaires.change || '0%',
      icon: ShieldCheckIcon,
      color: 'warning',
      subtitle: `${stats?.beneficiaires.male || 0}H / ${stats?.beneficiaires.female || 0}F`
    }
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Bonjour, {user?.first_name} ! 👋
        </h1>
        <p className="text-gray-600">
          Voici un aperçu de votre plateforme CNSS aujourd'hui.
        </p>
      </motion.div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="relative overflow-hidden">
              <div className="flex items-center">
                <div className={`p-3 rounded-xl bg-${stat.color}-100`}>
                  <stat.icon className={`h-6 w-6 text-${stat.color}-600`} />
                </div>
                <div className="ml-4 flex-1">
                  <p className="text-sm font-medium text-gray-600">
                    {stat.title}
                  </p>
                  <div className="flex items-baseline">
                    <p className="text-2xl font-semibold text-gray-900">
                      {stat.value.toLocaleString()}
                    </p>
                    <p className={`ml-2 text-sm font-medium ${
                      stat.change.startsWith('+') ? 'text-success-600' : 'text-error-600'
                    }`}>
                      {stat.change}
                    </p>
                  </div>
                  {stat.subtitle && (
                    <p className="text-xs text-gray-500 mt-1">
                      {stat.subtitle}
                    </p>
                  )}
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <Card.Header>
              <Card.Title>Activité récente</Card.Title>
            </Card.Header>
            <Card.Content>
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      {getStatusIcon(activity.status)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900">
                        {activity.message}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatTimestamp(activity.timestamp)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </Card.Content>
          </Card>
        </motion.div>

        {/* System Health */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <Card.Header>
              <Card.Title>État du système</Card.Title>
            </Card.Header>
            <Card.Content>
              <div className="space-y-4">
                {systemHealth && Object.entries(systemHealth).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        value.status === 'healthy' ? 'bg-success-500' :
                        value.status === 'warning' ? 'bg-warning-500' : 'bg-error-500'
                      }`} />
                      <span className="text-sm font-medium text-gray-900 capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </span>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">
                        {value.responseTime || value.usage}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </Card.Content>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default Dashboard;
