import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  BuildingOfficeIcon,
  CogIcon,
  DocumentTextIcon,
  UsersIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import Card from '../components/ui/Card';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import { dashboardAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';

/**
 * Dashboard Page Component
 * Main dashboard with statistics and recent activity
 */
const Dashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState(null);
  const [recentActivity, setRecentActivity] = useState([]);
  const [systemHealth, setSystemHealth] = useState(null);
  const [loading, setLoading] = useState(true);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Try to fetch real data from API
        try {
          const [statsResponse, activityResponse, healthResponse] = await Promise.all([
            dashboardAPI.getStats(),
            dashboardAPI.getRecentActivity(),
            dashboardAPI.getSystemHealth()
          ]);

          setStats(statsResponse.data);
          setRecentActivity(activityResponse.data);
          setSystemHealth(healthResponse.data);
        } catch (apiError) {
          console.log('API not available, using mock data');

          // Fallback to mock data
          const mockStats = {
            employers: { total: 2, change: '+12%' },
            services: { total: 3, change: '+5%' },
            apiExchanges: { total: 156, change: '+23%' },
            users: { total: 2, change: '+2%' }
          };

          const mockActivity = [
            {
              id: 1,
              message: 'Nouvelle déclaration mensuelle soumise par TechCorp SARL',
              timestamp: '2024-01-25T10:30:00Z',
              status: 'success'
            },
            {
              id: 2,
              message: 'Paiement de cotisations effectué par InnovateTech SA',
              timestamp: '2024-01-25T09:15:00Z',
              status: 'success'
            }
          ];

          const mockSystemHealth = {
            database: { status: 'healthy', responseTime: '12ms' },
            apiGateway: { status: 'healthy', responseTime: '45ms' },
            externalApis: { status: 'warning', responseTime: '234ms' },
            storage: { status: 'healthy', usage: '67%' }
          };

          setStats(mockStats);
          setRecentActivity(mockActivity);
          setSystemHealth(mockSystemHealth);
        }

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon className="h-5 w-5 text-success-500" />;
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-warning-500" />;
      case 'error':
        return <ExclamationTriangleIcon className="h-5 w-5 text-error-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-primary-500" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  const statCards = [
    {
      title: 'Employeurs',
      value: stats?.employers.total || 0,
      change: stats?.employers.change || '0%',
      icon: BuildingOfficeIcon,
      color: 'primary'
    },
    {
      title: 'Services',
      value: stats?.services.total || 0,
      change: stats?.services.change || '0%',
      icon: CogIcon,
      color: 'success'
    },
    {
      title: 'Échanges API',
      value: stats?.apiExchanges.total || 0,
      change: stats?.apiExchanges.change || '0%',
      icon: DocumentTextIcon,
      color: 'warning'
    },
    {
      title: 'Utilisateurs',
      value: stats?.users.total || 0,
      change: stats?.users.change || '0%',
      icon: UsersIcon,
      color: 'secondary'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Bonjour, {user?.first_name} ! 👋
        </h1>
        <p className="text-gray-600">
          Voici un aperçu de votre plateforme CNSS aujourd'hui.
        </p>
      </motion.div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="relative overflow-hidden">
              <div className="flex items-center">
                <div className={`p-3 rounded-xl bg-${stat.color}-100`}>
                  <stat.icon className={`h-6 w-6 text-${stat.color}-600`} />
                </div>
                <div className="ml-4 flex-1">
                  <p className="text-sm font-medium text-gray-600">
                    {stat.title}
                  </p>
                  <div className="flex items-baseline">
                    <p className="text-2xl font-semibold text-gray-900">
                      {stat.value.toLocaleString()}
                    </p>
                    <p className={`ml-2 text-sm font-medium ${
                      stat.change.startsWith('+') ? 'text-success-600' : 'text-error-600'
                    }`}>
                      {stat.change}
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <Card.Header>
              <Card.Title>Activité récente</Card.Title>
            </Card.Header>
            <Card.Content>
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      {getStatusIcon(activity.status)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900">
                        {activity.message}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatTimestamp(activity.timestamp)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </Card.Content>
          </Card>
        </motion.div>

        {/* System Health */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <Card.Header>
              <Card.Title>État du système</Card.Title>
            </Card.Header>
            <Card.Content>
              <div className="space-y-4">
                {systemHealth && Object.entries(systemHealth).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        value.status === 'healthy' ? 'bg-success-500' :
                        value.status === 'warning' ? 'bg-warning-500' : 'bg-error-500'
                      }`} />
                      <span className="text-sm font-medium text-gray-900 capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </span>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">
                        {value.responseTime || value.usage}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </Card.Content>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default Dashboard;
