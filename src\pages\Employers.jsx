import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  BuildingOfficeIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import EmployerModal from '../components/modals/EmployerModal';
import { employersAPI } from '../services/api';
import toast from 'react-hot-toast';

/**
 * Employers Management Page
 * Full CRUD operations for employer management
 */
const Employers = () => {
  const [employers, setEmployers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedEmployer, setSelectedEmployer] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [modalMode, setModalMode] = useState('view'); // 'view', 'create', 'edit'

  // Fetch employers
  useEffect(() => {
    fetchEmployers();
  }, []);

  const fetchEmployers = async () => {
    try {
      setLoading(true);

      try {
        const response = await employersAPI.getAll({ search: searchTerm });
        setEmployers(response.data);
      } catch (apiError) {
        console.log('API not available, using mock data');

        // Fallback to mock data
        const mockEmployers = [
          {
            id: 1,
            company_name: "TechCorp SARL",
            registration_number: "RC123456789",
            tax_id: "TAX987654321",
            cnss_number: "CNSS001234567",
            email: "<EMAIL>",
            phone: "+216 71 123 456",
            address_line1: "123 Avenue Habib Bourguiba",
            city: "Tunis",
            postal_code: "1000",
            region: "Tunis",
            legal_rep_name: "Ahmed Ben Ali",
            legal_rep_title: "Directeur Général",
            is_active: true,
            is_verified: true,
            created_at: "2024-01-15T10:30:00Z"
          },
          {
            id: 2,
            company_name: "InnovateTech SA",
            registration_number: "RC987654321",
            tax_id: "TAX123456789",
            cnss_number: "CNSS007654321",
            email: "<EMAIL>",
            phone: "+216 71 987 654",
            address_line1: "456 Rue de la République",
            city: "Sfax",
            postal_code: "3000",
            region: "Sfax",
            legal_rep_name: "Fatma Trabelsi",
            legal_rep_title: "PDG",
            is_active: true,
            is_verified: true,
            created_at: "2024-01-20T14:15:00Z"
          }
        ];

        setEmployers(mockEmployers);
      }
    } catch (error) {
      console.error('Error fetching employers:', error);
    } finally {
      setLoading(false);
    }
  };

  // Search functionality
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchTerm !== '') {
        fetchEmployers();
      }
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm]);

  const handleView = (employer) => {
    setSelectedEmployer(employer);
    setModalMode('view');
    setShowModal(true);
  };

  const handleEdit = (employer) => {
    setSelectedEmployer(employer);
    setModalMode('edit');
    setShowModal(true);
  };

  const handleCreate = () => {
    setSelectedEmployer(null);
    setModalMode('create');
    setShowModal(true);
  };

  const handleDelete = async (employerId) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cet employeur ?')) {
      try {
        await employersAPI.delete(employerId);
        toast.success('Employeur supprimé avec succès');
        fetchEmployers();
      } catch (error) {
        console.error('Error deleting employer:', error);
        toast.error('Erreur lors de la suppression');
      }
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusBadge = (employer) => {
    if (!employer.is_active) {
      return <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">Inactif</span>;
    }
    if (!employer.is_verified) {
      return <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">En attente</span>;
    }
    return <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Vérifié</span>;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col sm:flex-row sm:items-center sm:justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Gestion des Employeurs
          </h1>
          <p className="text-gray-600">
            Gérez les employeurs enregistrés dans le système CNSS
          </p>
        </div>
        <Button
          onClick={handleCreate}
          className="mt-4 sm:mt-0"
          icon={PlusIcon}
        >
          Nouvel Employeur
        </Button>
      </motion.div>

      {/* Search and Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <Card.Content className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  type="text"
                  placeholder="Rechercher par nom, numéro CNSS ou email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  icon={MagnifyingGlassIcon}
                />
              </div>
            </div>
          </Card.Content>
        </Card>
      </motion.div>

      {/* Employers Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <Card.Header>
            <Card.Title>
              <BuildingOfficeIcon className="w-5 h-5 mr-2" />
              Employeurs ({employers.length})
            </Card.Title>
          </Card.Header>
          <Card.Content className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Entreprise
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Numéro CNSS
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Contact
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Statut
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date création
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {employers.map((employer) => (
                    <tr key={employer.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {employer.company_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {employer.legal_rep_name} - {employer.legal_rep_title}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 font-mono">
                          {employer.cnss_number}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {employer.email}
                        </div>
                        <div className="text-sm text-gray-500">
                          {employer.phone}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(employer)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(employer.created_at)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <button
                            onClick={() => handleView(employer)}
                            className="text-blue-600 hover:text-blue-900"
                            title="Voir détails"
                          >
                            <EyeIcon className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleEdit(employer)}
                            className="text-indigo-600 hover:text-indigo-900"
                            title="Modifier"
                          >
                            <PencilIcon className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(employer.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Supprimer"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {employers.length === 0 && (
                <div className="text-center py-12">
                  <BuildingOfficeIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    Aucun employeur trouvé
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {searchTerm ? 'Aucun résultat pour votre recherche.' : 'Commencez par ajouter un nouvel employeur.'}
                  </p>
                </div>
              )}
            </div>
          </Card.Content>
        </Card>
      </motion.div>

      {/* Employer Modal */}
      <EmployerModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        mode={modalMode}
        employer={selectedEmployer}
        onSuccess={fetchEmployers}
      />
    </div>
  );
};

export default Employers;
