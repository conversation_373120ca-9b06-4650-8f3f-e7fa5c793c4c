import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  CogIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  PlayIcon,
  PauseIcon
} from '@heroicons/react/24/outline';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import { servicesAPI } from '../services/api';
import toast from 'react-hot-toast';

/**
 * Services Management Page
 * Manage API services and configurations
 */
const Services = () => {
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');

  // Fetch services
  useEffect(() => {
    fetchServices();
  }, []);

  const fetchServices = async () => {
    try {
      setLoading(true);

      try {
        const params = {};
        if (filterType !== 'all') params.service_type = filterType;
        if (filterStatus !== 'all') params.status = filterStatus;

        const response = await servicesAPI.getAll(params);
        setServices(response.data);
      } catch (apiError) {
        console.log('API not available, using mock data');

        // Fallback to mock data
        const mockServices = [
          {
            id: 1,
            name: "Déclaration Mensuelle",
            code: "DECL_MONTHLY",
            description: "Service de déclaration mensuelle des salaires et cotisations",
            service_type: "declaration",
            status: "active",
            endpoint_url: "/api/declarations/monthly",
            http_method: "POST",
            requires_auth: true,
            version: "1.0",
            created_at: "2024-01-01T00:00:00Z"
          },
          {
            id: 2,
            name: "Paiement Cotisations",
            code: "PAY_CONTRIB",
            description: "Service de paiement des cotisations sociales",
            service_type: "payment",
            status: "active",
            endpoint_url: "/api/payments/contributions",
            http_method: "POST",
            requires_auth: true,
            version: "1.0",
            created_at: "2024-01-01T00:00:00Z"
          },
          {
            id: 3,
            name: "Consultation Dossier",
            code: "INQ_DOSSIER",
            description: "Service de consultation du dossier employeur",
            service_type: "inquiry",
            status: "active",
            endpoint_url: "/api/inquiries/dossier",
            http_method: "GET",
            requires_auth: true,
            version: "1.0",
            created_at: "2024-01-01T00:00:00Z"
          }
        ];

        setServices(mockServices);
      }
    } catch (error) {
      console.error('Error fetching services:', error);
    } finally {
      setLoading(false);
    }
  };

  // Filter services based on search term
  const filteredServices = services.filter(service =>
    service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    service.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    service.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { bg: 'bg-green-100', text: 'text-green-800', label: 'Actif' },
      inactive: { bg: 'bg-red-100', text: 'text-red-800', label: 'Inactif' },
      maintenance: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Maintenance' },
      deprecated: { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Déprécié' }
    };

    const config = statusConfig[status] || statusConfig.inactive;
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  const getTypeBadge = (type) => {
    const typeConfig = {
      declaration: { bg: 'bg-blue-100', text: 'text-blue-800', label: 'Déclaration' },
      payment: { bg: 'bg-green-100', text: 'text-green-800', label: 'Paiement' },
      inquiry: { bg: 'bg-purple-100', text: 'text-purple-800', label: 'Consultation' },
      certificate: { bg: 'bg-orange-100', text: 'text-orange-800', label: 'Certificat' },
      report: { bg: 'bg-indigo-100', text: 'text-indigo-800', label: 'Rapport' }
    };

    const config = typeConfig[type] || typeConfig.inquiry;
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col sm:flex-row sm:items-center sm:justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Gestion des Services
          </h1>
          <p className="text-gray-600">
            Configurez et gérez les services API de la plateforme CNSS
          </p>
        </div>
        <Button
          onClick={() => toast.info('Fonctionnalité à venir')}
          className="mt-4 sm:mt-0"
          icon={PlusIcon}
        >
          Nouveau Service
        </Button>
      </motion.div>

      {/* Search and Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <Card.Content className="p-4">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <Input
                  type="text"
                  placeholder="Rechercher par nom, code ou description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  icon={MagnifyingGlassIcon}
                />
              </div>
              <div className="flex gap-4">
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="all">Tous les types</option>
                  <option value="declaration">Déclaration</option>
                  <option value="payment">Paiement</option>
                  <option value="inquiry">Consultation</option>
                  <option value="certificate">Certificat</option>
                  <option value="report">Rapport</option>
                </select>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="all">Tous les statuts</option>
                  <option value="active">Actif</option>
                  <option value="inactive">Inactif</option>
                  <option value="maintenance">Maintenance</option>
                  <option value="deprecated">Déprécié</option>
                </select>
              </div>
            </div>
          </Card.Content>
        </Card>
      </motion.div>

      {/* Services Grid */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        {filteredServices.map((service) => (
          <Card key={service.id} className="hover:shadow-lg transition-shadow">
            <Card.Header>
              <div className="flex items-start justify-between">
                <div className="flex items-center">
                  <CogIcon className="w-5 h-5 text-primary-600 mr-2" />
                  <Card.Title className="text-lg">{service.name}</Card.Title>
                </div>
                <div className="flex space-x-1">
                  {getStatusBadge(service.status)}
                </div>
              </div>
            </Card.Header>
            <Card.Content>
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-600 mb-2">{service.description}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-xs font-mono bg-gray-100 px-2 py-1 rounded">
                      {service.code}
                    </span>
                    {getTypeBadge(service.service_type)}
                  </div>
                </div>

                <div className="border-t pt-3">
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-2">
                    <span>Méthode:</span>
                    <span className="font-mono bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                      {service.http_method}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-2">
                    <span>Version:</span>
                    <span>{service.version}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>Créé le:</span>
                    <span>{formatDate(service.created_at)}</span>
                  </div>
                </div>

                {service.endpoint_url && (
                  <div className="border-t pt-3">
                    <p className="text-xs text-gray-500 mb-1">Endpoint:</p>
                    <code className="text-xs bg-gray-100 p-2 rounded block break-all">
                      {service.endpoint_url}
                    </code>
                  </div>
                )}
              </div>
            </Card.Content>
            <Card.Footer>
              <div className="flex justify-between items-center">
                <div className="flex space-x-2">
                  <button
                    onClick={() => toast.info('Fonctionnalité à venir')}
                    className="text-blue-600 hover:text-blue-900"
                    title="Voir détails"
                  >
                    <EyeIcon className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => toast.info('Fonctionnalité à venir')}
                    className="text-indigo-600 hover:text-indigo-900"
                    title="Modifier"
                  >
                    <PencilIcon className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => toast.info('Fonctionnalité à venir')}
                    className="text-red-600 hover:text-red-900"
                    title="Supprimer"
                  >
                    <TrashIcon className="w-4 h-4" />
                  </button>
                </div>
                <div className="flex space-x-2">
                  {service.status === 'active' ? (
                    <button
                      onClick={() => toast.info('Fonctionnalité à venir')}
                      className="text-yellow-600 hover:text-yellow-900"
                      title="Mettre en pause"
                    >
                      <PauseIcon className="w-4 h-4" />
                    </button>
                  ) : (
                    <button
                      onClick={() => toast.info('Fonctionnalité à venir')}
                      className="text-green-600 hover:text-green-900"
                      title="Activer"
                    >
                      <PlayIcon className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
            </Card.Footer>
          </Card>
        ))}
      </motion.div>

      {filteredServices.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="text-center py-12"
        >
          <CogIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            Aucun service trouvé
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm ? 'Aucun résultat pour votre recherche.' : 'Commencez par ajouter un nouveau service.'}
          </p>
        </motion.div>
      )}
    </div>
  );
};

export default Services;
