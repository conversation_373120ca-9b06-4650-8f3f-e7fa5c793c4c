import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  CogIcon,
  ServerIcon,
  ShieldCheckIcon,
  CircleStackIcon,
  PaintBrushIcon,
  SunIcon,
  MoonIcon,
  ComputerDesktopIcon
} from '@heroicons/react/24/outline';
import Card from '../components/ui/Card';
import Input from '../components/ui/Input';
import { useTheme } from '../contexts/ThemeContext';

/**
 * Settings/Parameters Page
 * System configuration and user preferences
 */
const Settings = () => {
  const { theme, setTheme, isDark } = useTheme();
  const [activeTab, setActiveTab] = useState('general');

  // Settings state
  const [settings, setSettings] = useState({
    general: {
      systemName: 'CNSS Platform',
      systemVersion: '1.0.0',
      maintenanceMode: false,
      debugMode: false,
      maxFileSize: '10',
      sessionTimeout: '30'
    },
    database: {
      host: 'localhost',
      port: '1521',
      serviceName: 'XEPDB1',
      maxConnections: '100',
      connectionTimeout: '30'
    },
    security: {
      passwordMinLength: '8',
      passwordRequireSpecial: true,
      maxLoginAttempts: '5',
      lockoutDuration: '15',
      tokenExpiration: '30'
    },

    api: {
      rateLimit: '1000',
      timeout: '30',
      retryCount: '3',
      enableLogging: true,
      enableMetrics: true
    }
  });

  const tabs = [
    { id: 'general', name: 'Général', icon: CogIcon },
    { id: 'appearance', name: 'Apparence', icon: PaintBrushIcon },
    { id: 'database', name: 'Base de données', icon: CircleStackIcon },
    { id: 'security', name: 'Sécurité', icon: ShieldCheckIcon },
    { id: 'api', name: 'API', icon: ServerIcon }
  ];

  const handleSettingChange = (category, key, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
  };



  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          label="Nom du système"
          value={settings.general.systemName}
          onChange={(e) => handleSettingChange('general', 'systemName', e.target.value)}
        />
        <Input
          label="Version"
          value={settings.general.systemVersion}
          onChange={(e) => handleSettingChange('general', 'systemVersion', e.target.value)}
          disabled
        />
        <Input
          label="Taille max fichier (MB)"
          type="number"
          value={settings.general.maxFileSize}
          onChange={(e) => handleSettingChange('general', 'maxFileSize', e.target.value)}
        />
        <Input
          label="Timeout session (min)"
          type="number"
          value={settings.general.sessionTimeout}
          onChange={(e) => handleSettingChange('general', 'sessionTimeout', e.target.value)}
        />
      </div>

      <div className="space-y-4">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={settings.general.maintenanceMode}
            onChange={(e) => handleSettingChange('general', 'maintenanceMode', e.target.checked)}
            className={`rounded text-primary-600 focus:ring-primary-500 transition-colors duration-300 ${
              isDark
                ? 'border-gray-600 bg-gray-700 focus:ring-offset-gray-800'
                : 'border-gray-300 bg-white focus:ring-offset-white'
            }`}
          />
          <span className={`ml-2 text-sm transition-colors duration-300 ${
            isDark ? 'text-gray-300' : 'text-gray-700'
          }`}>Mode maintenance</span>
        </label>

        <label className="flex items-center">
          <input
            type="checkbox"
            checked={settings.general.debugMode}
            onChange={(e) => handleSettingChange('general', 'debugMode', e.target.checked)}
            className={`rounded text-primary-600 focus:ring-primary-500 transition-colors duration-300 ${
              isDark
                ? 'border-gray-600 bg-gray-700 focus:ring-offset-gray-800'
                : 'border-gray-300 bg-white focus:ring-offset-white'
            }`}
          />
          <span className={`ml-2 text-sm transition-colors duration-300 ${
            isDark ? 'text-gray-300' : 'text-gray-700'
          }`}>Mode debug</span>
        </label>
      </div>
    </div>
  );

  const renderDatabaseSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          label="Hôte"
          value={settings.database.host}
          onChange={(e) => handleSettingChange('database', 'host', e.target.value)}
        />
        <Input
          label="Port"
          type="number"
          value={settings.database.port}
          onChange={(e) => handleSettingChange('database', 'port', e.target.value)}
        />
        <Input
          label="Service Name"
          value={settings.database.serviceName}
          onChange={(e) => handleSettingChange('database', 'serviceName', e.target.value)}
        />
        <Input
          label="Max connexions"
          type="number"
          value={settings.database.maxConnections}
          onChange={(e) => handleSettingChange('database', 'maxConnections', e.target.value)}
        />
      </div>

      <div className={`border rounded-lg p-4 transition-colors duration-300 ${
        isDark
          ? 'bg-yellow-900/20 border-yellow-700'
          : 'bg-yellow-50 border-yellow-200'
      }`}>
        <div className="flex">
          <div className="flex-shrink-0">
            <CircleStackIcon className="h-5 w-5 text-yellow-400" />
          </div>
          <div className="ml-3">
            <h3 className={`text-sm font-medium transition-colors duration-300 ${
              isDark ? 'text-yellow-300' : 'text-yellow-800'
            }`}>
              Attention
            </h3>
            <div className={`mt-2 text-sm transition-colors duration-300 ${
              isDark ? 'text-yellow-400' : 'text-yellow-700'
            }`}>
              <p>
                Les modifications de la base de données nécessitent un redémarrage du système.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          label="Longueur min mot de passe"
          type="number"
          value={settings.security.passwordMinLength}
          onChange={(e) => handleSettingChange('security', 'passwordMinLength', e.target.value)}
        />
        <Input
          label="Max tentatives connexion"
          type="number"
          value={settings.security.maxLoginAttempts}
          onChange={(e) => handleSettingChange('security', 'maxLoginAttempts', e.target.value)}
        />
        <Input
          label="Durée verrouillage (min)"
          type="number"
          value={settings.security.lockoutDuration}
          onChange={(e) => handleSettingChange('security', 'lockoutDuration', e.target.value)}
        />
        <Input
          label="Expiration token (min)"
          type="number"
          value={settings.security.tokenExpiration}
          onChange={(e) => handleSettingChange('security', 'tokenExpiration', e.target.value)}
        />
      </div>

      <div className="space-y-4">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={settings.security.passwordRequireSpecial}
            onChange={(e) => handleSettingChange('security', 'passwordRequireSpecial', e.target.checked)}
            className={`rounded text-primary-600 focus:ring-primary-500 transition-colors duration-300 ${
              isDark
                ? 'border-gray-600 bg-gray-700 focus:ring-offset-gray-800'
                : 'border-gray-300 bg-white focus:ring-offset-white'
            }`}
          />
          <span className={`ml-2 text-sm transition-colors duration-300 ${
            isDark ? 'text-gray-300' : 'text-gray-700'
          }`}>Caractères spéciaux requis</span>
        </label>
      </div>
    </div>
  );



  const renderApiSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          label="Limite de taux (req/h)"
          type="number"
          value={settings.api.rateLimit}
          onChange={(e) => handleSettingChange('api', 'rateLimit', e.target.value)}
        />
        <Input
          label="Timeout (sec)"
          type="number"
          value={settings.api.timeout}
          onChange={(e) => handleSettingChange('api', 'timeout', e.target.value)}
        />
        <Input
          label="Nombre de tentatives"
          type="number"
          value={settings.api.retryCount}
          onChange={(e) => handleSettingChange('api', 'retryCount', e.target.value)}
        />
      </div>

      <div className="space-y-4">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={settings.api.enableLogging}
            onChange={(e) => handleSettingChange('api', 'enableLogging', e.target.checked)}
            className={`rounded text-primary-600 focus:ring-primary-500 transition-colors duration-300 ${
              isDark
                ? 'border-gray-600 bg-gray-700 focus:ring-offset-gray-800'
                : 'border-gray-300 bg-white focus:ring-offset-white'
            }`}
          />
          <span className={`ml-2 text-sm transition-colors duration-300 ${
            isDark ? 'text-gray-300' : 'text-gray-700'
          }`}>Activer les logs</span>
        </label>

        <label className="flex items-center">
          <input
            type="checkbox"
            checked={settings.api.enableMetrics}
            onChange={(e) => handleSettingChange('api', 'enableMetrics', e.target.checked)}
            className={`rounded text-primary-600 focus:ring-primary-500 transition-colors duration-300 ${
              isDark
                ? 'border-gray-600 bg-gray-700 focus:ring-offset-gray-800'
                : 'border-gray-300 bg-white focus:ring-offset-white'
            }`}
          />
          <span className={`ml-2 text-sm transition-colors duration-300 ${
            isDark ? 'text-gray-300' : 'text-gray-700'
          }`}>Activer les métriques</span>
        </label>
      </div>
    </div>
  );

  const renderAppearanceSettings = () => (
    <div className="space-y-6">
      <div>
        <h4 className={`text-lg font-medium mb-4 transition-colors duration-300 ${
          isDark ? 'text-white' : 'text-gray-900'
        }`}>Thème</h4>
        <p className={`text-sm mb-4 transition-colors duration-300 ${
          isDark ? 'text-gray-400' : 'text-gray-600'
        }`}>
          Choisissez l'apparence de l'interface utilisateur
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Light Theme */}
          <div
            onClick={() => setTheme('light')}
            className={`relative p-4 border-2 rounded-xl cursor-pointer transition-all ${
              theme === 'light'
                ? 'border-primary-500 bg-primary-50'
                : isDark
                  ? 'border-gray-600 hover:border-gray-500 bg-gray-700'
                  : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <SunIcon className="w-6 h-6 text-yellow-600" />
              </div>
              <div>
                <h5 className={`font-medium transition-colors duration-300 ${
                  isDark ? 'text-white' : 'text-gray-900'
                }`}>Clair</h5>
                <p className={`text-sm transition-colors duration-300 ${
                  isDark ? 'text-gray-400' : 'text-gray-500'
                }`}>Thème lumineux</p>
              </div>
            </div>
            {theme === 'light' && (
              <div className="absolute top-2 right-2">
                <div className="w-3 h-3 bg-primary-500 rounded-full"></div>
              </div>
            )}
          </div>

          {/* Dark Theme */}
          <div
            onClick={() => setTheme('dark')}
            className={`relative p-4 border-2 rounded-xl cursor-pointer transition-all ${
              theme === 'dark'
                ? 'border-primary-500 bg-primary-50'
                : isDark
                  ? 'border-gray-600 hover:border-gray-500 bg-gray-700'
                  : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gray-800 rounded-lg">
                <MoonIcon className="w-6 h-6 text-gray-100" />
              </div>
              <div>
                <h5 className={`font-medium transition-colors duration-300 ${
                  isDark ? 'text-white' : 'text-gray-900'
                }`}>Sombre</h5>
                <p className={`text-sm transition-colors duration-300 ${
                  isDark ? 'text-gray-400' : 'text-gray-500'
                }`}>Thème sombre</p>
              </div>
            </div>
            {theme === 'dark' && (
              <div className="absolute top-2 right-2">
                <div className="w-3 h-3 bg-primary-500 rounded-full"></div>
              </div>
            )}
          </div>

          {/* System Theme */}
          <div
            onClick={() => setTheme('system')}
            className={`relative p-4 border-2 rounded-xl cursor-pointer transition-all ${
              theme === 'system'
                ? 'border-primary-500 bg-primary-50'
                : isDark
                  ? 'border-gray-600 hover:border-gray-500 bg-gray-700'
                  : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <ComputerDesktopIcon className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h5 className={`font-medium transition-colors duration-300 ${
                  isDark ? 'text-white' : 'text-gray-900'
                }`}>Système</h5>
                <p className={`text-sm transition-colors duration-300 ${
                  isDark ? 'text-gray-400' : 'text-gray-500'
                }`}>Suit le système</p>
              </div>
            </div>
            {theme === 'system' && (
              <div className="absolute top-2 right-2">
                <div className="w-3 h-3 bg-primary-500 rounded-full"></div>
              </div>
            )}
          </div>
        </div>

        <div className={`mt-4 p-4 rounded-lg transition-colors duration-300 ${
          isDark ? 'bg-gray-700' : 'bg-gray-50'
        }`}>
          <p className={`text-sm transition-colors duration-300 ${
            isDark ? 'text-gray-300' : 'text-gray-600'
          }`}>
            <strong>Thème actuel:</strong> {theme === 'light' ? 'Clair' : theme === 'dark' ? 'Sombre' : 'Système'}
            {theme === 'system' && ` (${isDark ? 'Sombre' : 'Clair'} détecté)`}
          </p>
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return renderGeneralSettings();
      case 'appearance':
        return renderAppearanceSettings();
      case 'database':
        return renderDatabaseSettings();
      case 'security':
        return renderSecuritySettings();
      case 'api':
        return renderApiSettings();
      default:
        return renderGeneralSettings();
    }
  };

  return (
    <div className={`min-h-screen p-6 space-y-6 transition-colors duration-300 ${
      isDark ? 'bg-gray-900' : 'bg-gray-50'
    }`}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <h1 className={`text-3xl font-bold mb-2 transition-colors duration-300 ${
            isDark ? 'text-white' : 'text-gray-900'
          }`}>
            Paramètres du Système
          </h1>
          <p className={`transition-colors duration-300 ${
            isDark ? 'text-gray-300' : 'text-gray-600'
          }`}>
            Configurez les paramètres de la plateforme CNSS
          </p>
        </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
          className="lg:col-span-1"
        >
          <Card className={isDark ? 'bg-gray-800 border-gray-700' : 'bg-white'}>
            <Card.Content className="p-0">
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-none first:rounded-t-lg last:rounded-b-lg ${
                        activeTab === tab.id
                          ? isDark
                            ? 'bg-primary-900/20 text-primary-400 border-r-2 border-primary-400'
                            : 'bg-primary-50 text-primary-700 border-r-2 border-primary-500'
                          : isDark
                            ? 'text-gray-300 hover:bg-gray-700 hover:text-white'
                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    >
                      <Icon className="w-5 h-5 mr-3" />
                      {tab.name}
                    </button>
                  );
                })}
              </nav>
            </Card.Content>
          </Card>
        </motion.div>

        {/* Main Content */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="lg:col-span-3"
        >
          <Card className={isDark ? 'bg-gray-800 border-gray-700' : 'bg-white'}>
            <Card.Header>
              <Card.Title className={isDark ? 'text-white' : 'text-gray-900'}>
                {tabs.find(tab => tab.id === activeTab)?.name}
              </Card.Title>
            </Card.Header>
            <Card.Content>
              {renderTabContent()}
            </Card.Content>
          </Card>
        </motion.div>
      </div>
      </div>
    </div>
  );
};

export default Settings;
