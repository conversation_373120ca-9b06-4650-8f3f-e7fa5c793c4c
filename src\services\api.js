import axios from "axios";
import Cookies from "js-cookie";
import toast from "react-hot-toast";

// API Base URL - will be configured via environment variables
const API_BASE_URL =
  process.env.REACT_APP_API_URL || "http://localhost:8001/api/v1";

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get("cnss_token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    const { response } = error;

    if (response?.status === 401) {
      // Unauthorized - clear auth and redirect to login
      Cookies.remove("cnss_token");
      localStorage.removeItem("cnss_user");
      window.location.href = "/login";
      toast.error("Session expirée. Veuillez vous reconnecter.");
    } else if (response?.status === 403) {
      toast.error("Accès non autorisé.");
    } else if (response?.status >= 500) {
      console.log("Server error:", error);
    } else if (!response) {
      console.log("Connection error:", error);
    }

    return Promise.reject(error);
  }
);

// Authentication API
export const authAPI = {
  login: (credentials) => api.post("/auth/login", credentials),
  logout: () => api.post("/auth/logout"),
  refreshToken: () => api.post("/auth/refresh"),
  getCurrentUser: () => api.get("/auth/me"),
};

// Users API
export const usersAPI = {
  getAll: (params = {}) => api.get("/users", { params }),
  getById: (id) => api.get(`/users/${id}`),
  create: (userData) => api.post("/users", userData),
  update: (id, userData) => api.put(`/users/${id}`, userData),
  delete: (id) => api.delete(`/users/${id}`),
  changePassword: (id, passwordData) =>
    api.put(`/users/${id}/password`, passwordData),
};

// Employers API
export const employersAPI = {
  getAll: (params = {}) => api.get("/employers", { params }),
  getById: (id) => api.get(`/employers/${id}`),
  create: (employerData) => api.post("/employers", employerData),
  update: (id, employerData) => api.put(`/employers/${id}`, employerData),
  delete: (id) => api.delete(`/employers/${id}`),
  search: (query) =>
    api.get(`/employers/search?q=${encodeURIComponent(query)}`),
};

// Services API
export const servicesAPI = {
  getAll: (params = {}) => api.get("/services", { params }),
  getById: (id) => api.get(`/services/${id}`),
  create: (serviceData) => api.post("/services", serviceData),
  update: (id, serviceData) => api.put(`/services/${id}`, serviceData),
  delete: (id) => api.delete(`/services/${id}`),
  getByEmployer: (employerId) => api.get(`/services/employer/${employerId}`),
};

// API Exchange API
export const apiExchangeAPI = {
  getAll: (params = {}) => api.get("/api-exchanges", { params }),
  getById: (id) => api.get(`/api-exchanges/${id}`),
  create: (exchangeData) => api.post("/api-exchanges", exchangeData),
  update: (id, exchangeData) => api.put(`/api-exchanges/${id}`, exchangeData),
  delete: (id) => api.delete(`/api-exchanges/${id}`),
  test: (id) => api.post(`/api-exchanges/${id}/test`),
  getLogs: (id, params = {}) =>
    api.get(`/api-exchanges/${id}/logs`, { params }),
};

// Dashboard API
export const dashboardAPI = {
  getStats: () => api.get("/dashboard/stats"),
  getRecentActivity: () => api.get("/dashboard/recent-activity"),
  getSystemHealth: () => api.get("/dashboard/system-health"),
};

// Admin API
export const adminAPI = {
  getSystemLogs: (params = {}) => api.get("/admin/logs", { params }),
  getSystemInfo: () => api.get("/admin/system-info"),
  backupDatabase: () => api.post("/admin/backup"),
  getBackups: () => api.get("/admin/backups"),
  restoreBackup: (backupId) => api.post(`/admin/restore/${backupId}`),
};

export default api;
