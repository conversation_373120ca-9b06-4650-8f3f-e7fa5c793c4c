@echo off
echo ========================================
echo CNSS Platform - Starting Backend Server
echo ========================================
echo.

echo Checking if Oracle is running...
docker ps | findstr oracle-xe >nul
if %errorlevel% neq 0 (
    echo WARNING: Oracle container is not running
    echo Please run setup_oracle_docker.bat first
    echo.
    echo Do you want to start Oracle now? (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        echo Starting Oracle...
        docker start oracle-xe
        echo Waiting for Oracle to be ready...
        timeout /t 15 /nobreak >nul
    ) else (
        echo Please start Oracle manually and try again
        pause
        exit /b 1
    )
)

echo ✅ Oracle is running

echo.
echo Navigating to backend directory...
cd backend

echo.
echo Activating virtual environment...
if exist venv\Scripts\activate.bat (
    call venv\Scripts\activate.bat
    echo ✅ Virtual environment activated
) else (
    echo Creating virtual environment...
    python -m venv venv
    call venv\Scripts\activate.bat
    echo Installing dependencies...
    pip install -r requirements.txt
    echo ✅ Virtual environment created and dependencies installed
)

echo.
echo Starting FastAPI server...
echo Server will be available at: http://localhost:8000
echo API Documentation: http://localhost:8000/docs
echo.
echo Press Ctrl+C to stop the server
echo.

python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

echo.
echo Backend server stopped
pause
