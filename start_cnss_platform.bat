@echo off
echo ========================================
echo 🚀 CNSS Platform - Complete Startup
echo ========================================
echo.

echo This script will start the complete CNSS platform:
echo 1. Oracle Database (Docker)
echo 2. FastAPI Backend Server
echo 3. React Frontend Development Server
echo.

echo Checking prerequisites...

:: Check Docker
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Docker is not installed or not running
    echo Please install Docker Desktop and make sure it's running
    pause
    exit /b 1
)
echo ✅ Docker is available

:: Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Node.js is not installed
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js is available

:: Check Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Python is not installed
    echo Please install Python from https://python.org/
    pause
    exit /b 1
)
echo ✅ Python is available

echo.
echo ========================================
echo Step 1: Starting Oracle Database
echo ========================================

:: Check if Oracle container exists and is running
docker ps | findstr oracle-xe >nul
if %errorlevel% neq 0 (
    echo Oracle container is not running. Checking if it exists...
    docker ps -a | findstr oracle-xe >nul
    if %errorlevel% neq 0 (
        echo Oracle container doesn't exist. Creating and starting...
        call setup_oracle_docker.bat
    ) else (
        echo Oracle container exists but is stopped. Starting...
        docker start oracle-xe
        echo Waiting for Oracle to be ready...
        timeout /t 20 /nobreak >nul
    )
) else (
    echo ✅ Oracle is already running
)

echo.
echo ========================================
echo Step 2: Starting Backend Server
echo ========================================

:: Start backend in a new window
start "CNSS Backend Server" cmd /k "cd backend && (if exist venv\Scripts\activate.bat (call venv\Scripts\activate.bat) else (python -m venv venv && call venv\Scripts\activate.bat && pip install -r requirements.txt)) && echo Backend starting at http://localhost:8000 && echo API Docs at http://localhost:8000/docs && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"

echo ✅ Backend server starting in new window...
echo Waiting for backend to initialize...
timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo Step 3: Starting Frontend Server
echo ========================================

:: Install frontend dependencies if needed
if not exist node_modules (
    echo Installing frontend dependencies...
    npm install
)

:: Start frontend in a new window
start "CNSS Frontend Server" cmd /k "echo Frontend starting at http://localhost:3000 && npm start"

echo ✅ Frontend server starting in new window...

echo.
echo ========================================
echo 🎉 CNSS Platform Started Successfully!
echo ========================================
echo.
echo Your CNSS platform is now running:
echo.
echo 🌐 Frontend (React):     http://localhost:3000
echo 🔧 Backend (FastAPI):    http://localhost:8000
echo 📚 API Documentation:    http://localhost:8000/docs
echo 🗄️  Oracle Database:     localhost:1521/XEPDB1
echo.
echo Database Connection Details:
echo - Username: cnss_user
echo - Password: cnss_password
echo - Service: XEPDB1
echo.
echo To stop the platform:
echo 1. Close the backend and frontend terminal windows
echo 2. Run: docker stop oracle-xe
echo.
echo To connect to Oracle manually:
echo docker exec -it oracle-xe sqlplus cnss_user/cnss_password@XEPDB1
echo.
echo Enjoy your CNSS platform! 🚀
echo.
pause
